'use client'
import axios from 'axios'
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

const AddUserPage = () => {
  const [activeTab, setActiveTab] = useState('add'); // 'add' or 'manage'
  const [userData, setUserData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user'
  })
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [roleFilter, setRoleFilter] = useState('all'); // 'all', 'admin', or 'user'
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch users when the manage tab is active
  useEffect(() => {
    if (activeTab === 'manage') {
      fetchUsers();
    }
  }, [activeTab]);

  // Apply filters whenever users, roleFilter, or searchTerm changes
  useEffect(() => {
    applyFilters();
  }, [users, roleFilter, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/users');
      if (response.data.success) {
        setUsers(response.data.users);
      } else {
        toast.error("Failed to load users");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let result = [...users];
    
    // Apply role filter
    if (roleFilter !== 'all') {
      result = result.filter(user => user.role === roleFilter);
    }
    
    // Apply search filter (if search term exists)
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      result = result.filter(user => 
        user.email.toLowerCase().includes(term)
      );
    }
    
    setFilteredUsers(result);
  };

  const handleChange = (e) => {
    setUserData({...userData, [e.target.name]: e.target.value})
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Validate passwords match
    if (userData.password !== userData.confirmPassword) {
      toast.error("Passwords do not match")
      return
    }

    try {
      setLoading(true);
      const response = await axios.post('/api/register', {
        email: userData.email,
        password: userData.password,
        role: userData.role
      })

      if (response.data.success) {
        toast.success("User created successfully")
        // Reset form
        setUserData({
          email: '',
          password: '',
          confirmPassword: '',
          role: 'user'
        })
        // Switch to manage tab and refresh user list
        setActiveTab('manage');
      } else {
        toast.error(response.data.message || "Failed to create user")
      }
    } catch (error) {
      console.error("User creation error:", error)
      toast.error(error.response?.data?.message || "Failed to create user")
    } finally {
      setLoading(false);
    }
  }

  const handleDeleteUser = async (userId) => {
    if (!window.confirm("Are you sure you want to delete this user?")) {
      return;
    }
    
    try {
      setLoading(true);
      const response = await axios.delete('/api/users', {
        params: { id: userId }
      });
      
      if (response.data.success) {
        toast.success("User deleted successfully");
        // Update users list with the returned data
        if (response.data.users) {
          setUsers(response.data.users);
        } else {
          // Fallback to fetching users if not returned
          await fetchUsers();
        }
      } else {
        toast.error(response.data.message || "Failed to delete user");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error(error.response?.data?.message || "Failed to delete user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>User Management</h1>
      
      {/* Tabs - Made more prominent with better styling */}
      <div className="flex border-b border-gray-300 mb-6">
        <button 
          className={`py-3 px-6 font-medium rounded-t-lg ${
            activeTab === 'add' 
              ? 'bg-black text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('add')}
        >
          Add New User
        </button>
        <button 
          className={`py-3 px-6 font-medium rounded-t-lg ml-2 ${
            activeTab === 'manage' 
              ? 'bg-black text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('manage')}
        >
          Manage Users
        </button>
      </div>
      
      {/* Add User Form */}
      {activeTab === 'add' && (
        <form onSubmit={handleSubmit} className='max-w-[500px] bg-white p-6 rounded-lg shadow-md'>
          <h2 className="text-xl font-semibold mb-4">Create New User Account</h2>
          
          <div className='mb-4'>
            <label className='block text-gray-700 mb-2'>Email</label>
            <input 
              type='email'
              name='email'
              value={userData.email}
              onChange={handleChange}
              className='w-full px-4 py-3 border border-gray-300 rounded-md'
              placeholder='<EMAIL>'
              required
            />
          </div>
          
          <div className='mb-4'>
            <label className='block text-gray-700 mb-2'>Password</label>
            <input 
              type='password'
              name='password'
              value={userData.password}
              onChange={handleChange}
              className='w-full px-4 py-3 border border-gray-300 rounded-md'
              placeholder='Enter password'
              required
            />
          </div>
          
          <div className='mb-4'>
            <label className='block text-gray-700 mb-2'>Confirm Password</label>
            <input 
              type='password'
              name='confirmPassword'
              value={userData.confirmPassword}
              onChange={handleChange}
              className='w-full px-4 py-3 border border-gray-300 rounded-md'
              placeholder='Confirm password'
              required
            />
          </div>
          
          <div className='mb-6'>
            <label className='block text-gray-700 mb-2'>User Role</label>
            <select
              name='role'
              value={userData.role}
              onChange={handleChange}
              className='w-full px-4 py-3 border border-gray-300 rounded-md'
              required
            >
              <option value='user'>User</option>
              <option value='admin'>Admin</option>
            </select>
          </div>
          
          <button 
            type='submit'
            className='w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors'
            disabled={loading}
          >
            {loading ? 'Creating...' : 'Create User'}
          </button>
        </form>
      )}
      
      {/* Manage Users Table */}
      {activeTab === 'manage' && (
        <div className='max-w-[800px] bg-white p-6 rounded-lg shadow-md'>
          <h2 className="text-xl font-semibold mb-4">Manage User Accounts</h2>
          
          {/* Filter Controls */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Search by Email</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search users..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Role</label>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Users</option>
                <option value="admin">Admins Only</option>
                <option value="user">Regular Users Only</option>
              </select>
            </div>
          </div>
          
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
              <span className="ml-2">Loading users...</span>
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className='relative overflow-x-auto border border-gray-300 rounded-lg'>
              <table className='w-full text-sm text-left text-gray-500'>
                <thead className='text-xs text-gray-700 uppercase bg-gray-50'>
                  <tr>
                    <th scope='col' className='px-6 py-3'>Email</th>
                    <th scope='col' className='px-6 py-3'>Role</th>
                    <th scope='col' className='px-6 py-3'>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map(user => (
                    <tr key={user._id} className='bg-white border-b hover:bg-gray-50'>
                      <td className='px-6 py-4'>{user.email}</td>
                      <td className='px-6 py-4'>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          user.role === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className='px-6 py-4'>
                        <button
                          onClick={() => handleDeleteUser(user._id)}
                          className='text-white bg-red-600 hover:bg-red-700 px-3 py-1 rounded-md text-sm'
                          disabled={loading}
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>
                {users.length === 0 
                  ? "No users found." 
                  : "No users match your filters."}
              </p>
              {users.length === 0 && (
                <button 
                  onClick={() => setActiveTab('add')}
                  className="mt-4 text-blue-600 hover:underline"
                >
                  Add your first user
                </button>
              )}
              {users.length > 0 && (
                <button 
                  onClick={() => {
                    setRoleFilter('all');
                    setSearchTerm('');
                  }}
                  className="mt-4 text-blue-600 hover:underline"
                >
                  Clear filters
                </button>
              )}
            </div>
          )}
          
          {/* User Count Summary */}
          {users.length > 0 && (
            <div className="mt-4 text-sm text-gray-500">
              <p>
                Showing {filteredUsers.length} of {users.length} users
                {roleFilter !== 'all' && ` (filtered by ${roleFilter} role)`}
                {searchTerm && ` (filtered by search)`}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default AddUserPage
