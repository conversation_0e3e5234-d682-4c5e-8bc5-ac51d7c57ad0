import mongoose from "mongoose";

const Schema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    blogId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'blog',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

// Compound index to ensure a user can only favorite a blog once
Schema.index({ userId: 1, blogId: 1 }, { unique: true });

const FavoriteModel = mongoose.models.favorite || mongoose.model('favorite', Schema);

export default FavoriteModel;
