'use client'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'
import Link from 'next/link'

const CommentSettingsPage = () => {
  const [settings, setSettings] = useState({
    commentsEnabled: true,
    requireLogin: true,
    allowReplies: true,
    allowReactions: true,
    maxCommentLength: 1000,
    moderationEnabled: false
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [blogs, setBlogs] = useState([])
  const [selectedBlog, setSelectedBlog] = useState('')

  // Fetch current settings
  const fetchSettings = async (blogId = null) => {
    try {
      setLoading(true)
      const params = blogId ? { blogId } : {}
      const response = await axios.get('/api/comments/settings', { params })

      if (response.data.success) {
        setSettings(response.data.settings)
      } else {
        toast.error('Failed to load settings')
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      toast.error('Failed to load settings')
    } finally {
      setLoading(false)
    }
  }

  // Fetch blogs for dropdown
  const fetchBlogs = async () => {
    try {
      const response = await axios.get('/api/blog')
      if (response.data.success) {
        setBlogs(response.data.blogs || [])
      }
    } catch (error) {
      console.error('Error fetching blogs:', error)
    }
  }

  // Save settings
  const saveSettings = async () => {
    try {
      setSaving(true)
      const authToken = localStorage.getItem('authToken')
      
      const payload = {
        ...settings,
        blogId: selectedBlog || null
      }

      const response = await axios.post('/api/comments/settings', payload, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (response.data.success) {
        toast.success('Settings saved successfully')
      } else {
        toast.error(response.data.message || 'Failed to save settings')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      if (error.response?.status === 403) {
        toast.error('Admin access required')
      } else {
        toast.error(error.response?.data?.message || 'Failed to save settings')
      }
    } finally {
      setSaving(false)
    }
  }

  // Handle input changes
  const handleChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle blog selection change
  const handleBlogChange = (blogId) => {
    setSelectedBlog(blogId)
    fetchSettings(blogId)
  }

  useEffect(() => {
    fetchSettings()
    fetchBlogs()
  }, [])

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <div className="flex items-center justify-between mb-6">
        <h1 className='text-2xl font-bold'>Comment Settings</h1>
        <Link 
          href="/admin/comments"
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          Back to Comments
        </Link>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-2">Loading settings...</p>
        </div>
      ) : (
        <div className="max-w-2xl">
          {/* Blog Selection */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
            <h2 className="text-lg font-semibold mb-4">Settings Scope</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Apply settings to:
                </label>
                <select
                  value={selectedBlog}
                  onChange={(e) => handleBlogChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Global Settings (All Blogs)</option>
                  {blogs.map(blog => (
                    <option key={blog._id} value={blog._id}>
                      {blog.title}
                    </option>
                  ))}
                </select>
                <p className="text-sm text-gray-600 mt-1">
                  {selectedBlog 
                    ? 'These settings will only apply to the selected blog'
                    : 'These settings will apply to all blogs unless overridden'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Comment Settings */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
            <h2 className="text-lg font-semibold mb-4">Comment Features</h2>
            <div className="space-y-4">
              {/* Comments Enabled */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Enable Comments
                  </label>
                  <p className="text-sm text-gray-600">
                    Allow users to post comments on blog posts
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.commentsEnabled}
                    onChange={(e) => handleChange('commentsEnabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Require Login */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Require Login
                  </label>
                  <p className="text-sm text-gray-600">
                    Users must be logged in to comment
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireLogin}
                    onChange={(e) => handleChange('requireLogin', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Allow Replies */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Allow Replies
                  </label>
                  <p className="text-sm text-gray-600">
                    Users can reply to comments
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.allowReplies}
                    onChange={(e) => handleChange('allowReplies', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Allow Reactions */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Allow Reactions
                  </label>
                  <p className="text-sm text-gray-600">
                    Users can like/dislike comments
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.allowReactions}
                    onChange={(e) => handleChange('allowReactions', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Max Comment Length */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Comment Length
                </label>
                <input
                  type="number"
                  min="100"
                  max="5000"
                  value={settings.maxCommentLength}
                  onChange={(e) => handleChange('maxCommentLength', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-sm text-gray-600 mt-1">
                  Characters (100-5000)
                </p>
              </div>

              {/* Moderation */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Enable Moderation
                  </label>
                  <p className="text-sm text-gray-600">
                    Comments require approval before appearing
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.moderationEnabled}
                    onChange={(e) => handleChange('moderationEnabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={saveSettings}
              disabled={saving}
              className={`px-6 py-2 rounded-md transition-colors ${
                saving
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommentSettingsPage
