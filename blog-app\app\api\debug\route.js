import { NextResponse } from "next/server";
import { verifyToken } from "@/lib/utils/token";

export async function GET(request) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false, 
        message: "Missing or invalid Authorization header" 
      }, { status: 401 });
    }
    
    const token = authHeader.split(' ')[1];
    if (!token) {
      return NextResponse.json({ 
        success: false, 
        message: "No token found in Authorization header" 
      }, { status: 401 });
    }
    
    const userData = verifyToken(token);
    if (!userData) {
      return NextResponse.json({ 
        success: false, 
        message: "Invalid token" 
      }, { status: 401 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: "Token is valid",
      user: {
        userId: userData.userId,
        role: userData.role,
        exp: userData.exp
      }
    });
  } catch (error) {
    console.error("Debug endpoint error:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Server error" 
    }, { status: 500 });
  }
}