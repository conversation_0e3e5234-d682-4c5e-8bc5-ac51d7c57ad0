import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import { verifyToken } from '@/lib/utils/token';
import CommentModel from '@/lib/models/CommentModel';
import CommentReactionModel from '@/lib/models/CommentReactionModel';
import CommentSettingsModel from '@/lib/models/CommentSettingsModel';

// Helper function to get user from token
function getUserFromToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.split(' ')[1];
  return verifyToken(token);
}

// Helper function to check if reactions are enabled
async function checkReactionsEnabled(blogId = null) {
  try {
    // Check blog-specific settings first, then global settings
    let settings = null;
    if (blogId) {
      settings = await CommentSettingsModel.findOne({ blogId });
    }
    if (!settings) {
      settings = await CommentSettingsModel.findOne({ blogId: null });
    }
    
    return settings ? settings.allowReactions : true; // Default to enabled
  } catch (error) {
    console.error('Error checking reaction settings:', error);
    return true; // Default to enabled on error
  }
}

// POST - Add or update a reaction to a comment
export async function POST(request, { params }) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ 
        success: false, 
        message: "Authentication required" 
      }, { status: 401 });
    }

    const commentId = params.id;
    const body = await request.json();
    const { reactionType } = body;

    if (!reactionType || !['like', 'dislike'].includes(reactionType)) {
      return NextResponse.json({ 
        success: false, 
        message: "Valid reaction type (like/dislike) is required" 
      }, { status: 400 });
    }

    // Verify comment exists
    const comment = await CommentModel.findById(commentId);
    if (!comment || comment.isDeleted) {
      return NextResponse.json({ 
        success: false, 
        message: "Comment not found" 
      }, { status: 404 });
    }

    // Check if reactions are enabled
    const reactionsEnabled = await checkReactionsEnabled(comment.blogId);
    if (!reactionsEnabled) {
      return NextResponse.json({ 
        success: false, 
        message: "Reactions are disabled for this blog" 
      }, { status: 403 });
    }

    // Check if user already has a reaction on this comment
    const existingReaction = await CommentReactionModel.findOne({
      commentId,
      userId: userData.userId
    });

    if (existingReaction) {
      if (existingReaction.reactionType === reactionType) {
        // Same reaction - remove it (toggle off)
        await CommentReactionModel.deleteOne({ _id: existingReaction._id });
        
        // Get updated counts
        const likeCount = await CommentReactionModel.countDocuments({ 
          commentId, 
          reactionType: 'like' 
        });
        const dislikeCount = await CommentReactionModel.countDocuments({ 
          commentId, 
          reactionType: 'dislike' 
        });

        return NextResponse.json({ 
          success: true, 
          message: "Reaction removed",
          reaction: null,
          likeCount,
          dislikeCount
        });
      } else {
        // Different reaction - update it
        existingReaction.reactionType = reactionType;
        await existingReaction.save();
        
        // Get updated counts
        const likeCount = await CommentReactionModel.countDocuments({ 
          commentId, 
          reactionType: 'like' 
        });
        const dislikeCount = await CommentReactionModel.countDocuments({ 
          commentId, 
          reactionType: 'dislike' 
        });

        return NextResponse.json({ 
          success: true, 
          message: "Reaction updated",
          reaction: reactionType,
          likeCount,
          dislikeCount
        });
      }
    } else {
      // No existing reaction - create new one
      await CommentReactionModel.create({
        commentId,
        userId: userData.userId,
        reactionType
      });
      
      // Get updated counts
      const likeCount = await CommentReactionModel.countDocuments({ 
        commentId, 
        reactionType: 'like' 
      });
      const dislikeCount = await CommentReactionModel.countDocuments({ 
        commentId, 
        reactionType: 'dislike' 
      });

      return NextResponse.json({ 
        success: true, 
        message: "Reaction added",
        reaction: reactionType,
        likeCount,
        dislikeCount
      });
    }
  } catch (error) {
    console.error("Error handling comment reaction:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to process reaction" 
    }, { status: 500 });
  }
}

// GET - Get user's reaction status for a comment
export async function GET(request, { params }) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ 
        success: false, 
        message: "Authentication required" 
      }, { status: 401 });
    }

    const commentId = params.id;

    // Get user's reaction
    const userReaction = await CommentReactionModel.findOne({
      commentId,
      userId: userData.userId
    });

    // Get total counts
    const likeCount = await CommentReactionModel.countDocuments({ 
      commentId, 
      reactionType: 'like' 
    });
    const dislikeCount = await CommentReactionModel.countDocuments({ 
      commentId, 
      reactionType: 'dislike' 
    });

    return NextResponse.json({ 
      success: true,
      userReaction: userReaction ? userReaction.reactionType : null,
      likeCount,
      dislikeCount
    });
  } catch (error) {
    console.error("Error fetching comment reaction:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch reaction status" 
    }, { status: 500 });
  }
}
