'use client'
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { setCookie, getCookie, setAnalyticsCookies, setFunctionalCookies } from '@/utils/cookieUtils'

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false)
  
  useEffect(() => {
    // Check if user has already made a choice
    const consentCookie = getCookie('cookie-consent')
    
    if (!consentCookie) {
      // Set a timeout to show the consent popup after 10 seconds
      const timer = setTimeout(() => {
        setShowConsent(true)
      }, 10000) // 10 seconds
      
      return () => clearTimeout(timer)
    } else if (consentCookie === 'accepted') {
      // If user has accepted cookies, set the necessary cookies
      setAnalyticsCookies()
      setFunctionalCookies()
    }
  }, [])
  
  const acceptCookies = () => {
    // Set cookie with 1-year expiry
    setCookie('cookie-consent', 'accepted', { expires: 365 })
    
    // Set other cookies as needed
    setAnalyticsCookies()
    setFunctionalCookies()
    
    setShowConsent(false)
    toast.success('Cookie preferences saved')
  }
  
  const declineCookies = () => {
    // Set cookie to remember user declined
    setCookie('cookie-consent', 'declined', { expires: 365 })
    setShowConsent(false)
    toast.info('Cookies declined. Some features may be limited.')
  }
  
  if (!showConsent) return null
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2">We use cookies</h3>
            <p className="text-gray-600 text-sm md:text-base">
              We use cookies to enhance your browsing experience, serve personalized ads or content, 
              and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.
              <a href="/privacy-policy" className="text-blue-600 hover:underline ml-1">
                Read our Cookie Policy
              </a>
            </p>
          </div>
          <div className="flex gap-3">
            <button 
              onClick={declineCookies}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base"
            >
              Decline
            </button>
            <button 
              onClick={acceptCookies}
              className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base"
            >
              Accept All
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CookieConsent