import { NextResponse } from "next/server";
import { ConnectDB } from "@/lib/config/db";
import UserModel from "@/lib/models/UserModel";
import { createToken } from "@/lib/utils/token";

// Connect to database
ConnectDB();

// Login endpoint
export async function POST(request) {
    try {
        const { email, password } = await request.json();
        
        // Find user with matching email
        const user = await UserModel.findOne({ email });
        
        // Check if user exists and password matches
        if (user && user.password === password) {
            // Generate token with user ID
            const token = createToken({ 
                userId: user._id.toString(),
                role: user.role
            });
            
            console.log("Generated token for user:", user._id.toString());
            
            return NextResponse.json({
                success: true,
                token: token,
                user: {
                    id: user._id.toString(),
                    email: user.email,
                    role: user.role,
                    name: user.name || '',
                    profilePicture: user.profilePicture || '/default_profile.png'
                }
            });
        } else {
            return NextResponse.json({
                success: false,
                message: "Invalid credentials"
            }, { status: 401 });
        }
    } catch (error) {
        console.error("Login error:", error);
        return NextResponse.json({
            success: false,
            message: "Server error"
        }, { status: 500 });
    }
}


