import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import { verifyToken } from '@/lib/utils/token';
import BlogModel from '@/lib/models/BlogModel';
import LikeModel from '@/models/Like';

// Helper function to get user from token
function getUserFromToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.split(' ')[1];
  return verifyToken(token);
}

// Check if user has liked a blog
export async function GET(request) {
  try {
    await ConnectDB();
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('blogId');
    
    const like = await LikeModel.findOne({ userId: userData.userId, blogId });
    
    return NextResponse.json({ 
      success: true, 
      isLiked: !!like 
    });
  } catch (error) {
    console.error("Error checking like status:", error);
    return NextResponse.json({ success: false, message: "Failed to check like status" }, { status: 500 });
  }
}

// Add a like
export async function POST(request) {
  try {
    await ConnectDB();
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const body = await request.json();
    const { blogId } = body;
    
    const existingLike = await LikeModel.findOne({ userId: userData.userId, blogId });
    if (existingLike) {
      return NextResponse.json({ success: true, message: "Blog already liked" });
    }
    
    await LikeModel.create({
      userId: userData.userId,
      blogId
    });
    
    return NextResponse.json({ success: true, message: "Blog liked" });
  } catch (error) {
    console.error("Error liking blog:", error);
    return NextResponse.json({ success: false, message: "Failed to like blog" }, { status: 500 });
  }
}

// Remove a like
export async function DELETE(request) {
  try {
    await ConnectDB();
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('blogId');
    
    await LikeModel.findOneAndDelete({ userId: userData.userId, blogId });
    
    return NextResponse.json({ success: true, message: "Like removed" });
  } catch (error) {
    console.error("Error removing like:", error);
    return NextResponse.json({ success: false, message: "Failed to remove like" }, { status: 500 });
  }
}
