"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/addBlog/page",{

/***/ "(app-pages-browser)/./app/admin/addBlog/page.jsx":
/*!************************************!*\
  !*** ./app/admin/addBlog/page.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Components/AdminComponents/BlogTableItem */ \"(app-pages-browser)/./Components/AdminComponents/BlogTableItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BlogManagementPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"add\"); // 'add' or 'manage'\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"\",\n        commentsEnabled: true\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [tempBlogId, setTempBlogId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        fetchCategories();\n        fetchAuthors();\n        if (activeTab === \"manage\") {\n            fetchBlogs();\n        }\n    }, [\n        activeTab\n    ]);\n    // Generate a temporary blog ID for image uploads when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n    }, []);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n                setData((prev)=>({\n                        ...prev,\n                        category: response.data.categories[0].name\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No categories found. Please add categories in Settings.\");\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load categories\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n                setData((prev)=>({\n                        ...prev,\n                        author: response.data.authors[0].name,\n                        authorId: response.data.authors[0]._id,\n                        authorImg: response.data.authors[0].image || \"/author_img.png\"\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No authors found. Please add authors in Settings.\");\n                setAuthors([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load authors\");\n            setAuthors([]);\n        }\n    };\n    const fetchBlogs = async ()=>{\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load blogs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const onChangeHandler = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"authorId\") {\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData({\n                    ...data,\n                    author: selectedAuthor.name,\n                    authorId: selectedAuthor._id,\n                    authorImg: selectedAuthor.image || \"/author_img.png\"\n                });\n            }\n        } else {\n            setData({\n                ...data,\n                [name]: value\n            });\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        if (!image) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select a blog thumbnail image\");\n            return;\n        }\n        if (!data.title.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter a blog title\");\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"title\", data.title);\n        formData.append(\"description\", data.description);\n        formData.append(\"category\", data.category);\n        formData.append(\"author\", data.author);\n        formData.append(\"authorId\", data.authorId);\n        formData.append(\"authorImg\", data.authorImg);\n        formData.append(\"image\", image);\n        formData.append(\"tempBlogId\", tempBlogId);\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog added successfully\");\n                setImage(false);\n                setData({\n                    title: \"\",\n                    description: \"\",\n                    category: categories.length > 0 ? categories[0].name : \"\",\n                    author: authors.length > 0 ? authors[0].name : \"\",\n                    authorId: authors.length > 0 ? authors[0]._id : \"\",\n                    authorImg: authors.length > 0 ? authors[0].image || \"/author_img.png\" : \"/author_img.png\"\n                });\n                // Reset temp blog ID and clear images\n                setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n                setAllImages([]);\n                // Switch to manage tab and refresh blog list\n                setActiveTab(\"manage\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.msg || \"Error adding blog\");\n            }\n        } catch (error) {\n            console.error(\"Error submitting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteBlog = async (mongoId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this blog?\")) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/blog\", {\n                params: {\n                    id: mongoId\n                }\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog deleted successfully\");\n            fetchBlogs();\n        } catch (error) {\n            console.error(\"Error deleting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    const insertBlogMention = (blogId, blogTitle)=>{\n        const mention = \"[[\".concat(blogId, \"|\").concat(blogTitle, \"]]\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        setShowBlogSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        if (!tempBlogId) return;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: tempBlogId,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", tempBlogId || \"new\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/images/\".concat(imageId));\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = \"{{image:\".concat(imageUrl, \"|\").concat(filename, \"}}\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Blog Management\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 325,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex border-b border-gray-300 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg \".concat(activeTab === \"add\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"add\"),\n                        children: \"Add New Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 329,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg ml-2 \".concat(activeTab === \"manage\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"manage\"),\n                        children: \"Manage Blogs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 339,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 328,\n                columnNumber: 13\n            }, undefined),\n            activeTab === \"add\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onSubmitHandler,\n                className: \"max-w-[800px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Create New Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 355,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Upload Thumbnail \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 86\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"image\",\n                                    className: \"cursor-pointer block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        className: \"border border-gray-300 rounded-md\",\n                                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                                        width: 200,\n                                        height: 120,\n                                        alt: \"\",\n                                        style: {\n                                            objectFit: \"cover\",\n                                            height: \"120px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    onChange: (e)=>setImage(e.target.files[0]),\n                                    type: \"file\",\n                                    id: \"image\",\n                                    hidden: true,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 357,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Blog Title \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    name: \"title\",\n                                    onChange: onChangeHandler,\n                                    value: data.title,\n                                    className: \"w-full px-4 py-3 border rounded-md\",\n                                    type: \"text\",\n                                    placeholder: \"Type here\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 372,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"blog-description\",\n                                        name: \"description\",\n                                        onChange: onChangeHandler,\n                                        value: data.description,\n                                        className: \"w-full px-4 py-3 border rounded-md\",\n                                        placeholder: \"Write content here\",\n                                        rows: 6,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center flex-wrap gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllBlogs();\n                                                setShowBlogSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Mention another blog\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllImages();\n                                                setShowImageSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Insert image\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Formats: [[blogId|blogTitle]] | \",\n                                                    \"{{image:url|filename}}\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 29\n                                }, undefined),\n                                showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Select a blog to mention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowBlogSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search blogs...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y\",\n                                                children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                        onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 relative mr-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: blog.image,\n                                                                    alt: blog.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: blog.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: blog.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, blog._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 33\n                                }, undefined),\n                                showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Insert Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowImageSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"Upload New Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: handleImageUpload,\n                                                                disabled: !selectedImageFile || imageUploadLoading,\n                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                                children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search images...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: imageSearchTerm,\n                                                onChange: (e)=>setImageSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                                children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative mb-2\",\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        src: image.url,\n                                                                        alt: image.filename,\n                                                                        fill: true,\n                                                                        className: \"object-cover rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            deleteImage(image._id, image.url);\n                                                                        },\n                                                                        className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        title: \"Delete image\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                                lineNumber: 560,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 65\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 truncate\",\n                                                                        children: image.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: new Date(image.uploadDate).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, image._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No images found. Upload your first image above.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 385,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading categories...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 33\n                                }, undefined) : categories.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    onChange: onChangeHandler,\n                                    value: data.category,\n                                    className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                    required: true,\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.name,\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No categories available. Please add categories in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 582,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading authors...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 33\n                                }, undefined) : authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"authorId\",\n                                            onChange: onChangeHandler,\n                                            value: data.authorId,\n                                            className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                            required: true,\n                                            children: authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: author._id,\n                                                    children: author.name\n                                                }, author._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 45\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: data.authorImg,\n                                                alt: data.author,\n                                                className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No authors available. Please add authors in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 605,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors\",\n                            disabled: loading || categories.length === 0 || authors.length === 0,\n                            children: loading ? \"Creating...\" : \"Create Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 640,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                    lineNumber: 354,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 353,\n                columnNumber: 17\n            }, undefined),\n            activeTab === \"manage\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[850px] bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Manage Blog Posts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 654,\n                        columnNumber: 21\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 658,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: \"Loading blogs...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 659,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 657,\n                        columnNumber: 25\n                    }, undefined) : blogs.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative overflow-x-auto border border-gray-300 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm text-gray-500 table-fixed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Author\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: blogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            mongoId: item._id,\n                                            title: item.title,\n                                            author: item.author,\n                                            authorImg: item.authorImg,\n                                            date: item.date,\n                                            deleteBlog: deleteBlog\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 663,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 662,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No blogs found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 697,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"add\"),\n                                className: \"mt-4 text-blue-600 hover:underline\",\n                                children: \"Add your first blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 698,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 696,\n                        columnNumber: 25\n                    }, undefined),\n                    blogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-sm text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Showing \",\n                                blogs.length,\n                                \" blog posts\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 710,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 709,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 653,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n        lineNumber: 324,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BlogManagementPage, \"wnAKy5iwsa+kpMyQuoXKgho6Us0=\");\n_c = BlogManagementPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogManagementPage);\nvar _c;\n$RefreshReg$(_c, \"BlogManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/addBlog/page.jsx\n"));

/***/ })

});