"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/profile/route";
exports.ids = ["app/api/profile/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprofile%2Froute&page=%2Fapi%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprofile%2Froute&page=%2Fapi%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_profile_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/profile/route.js */ \"(rsc)/./app/api/profile/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/profile/route\",\n        pathname: \"/api/profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/profile/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\profile\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_profile_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/profile/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprofile%2Froute&page=%2Fapi%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/profile/route.js":
/*!**********************************!*\
  !*** ./app/api/profile/route.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_UserModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/models/UserModel */ \"(rsc)/./lib/models/UserModel.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Initialize database connection\nconst LoadDB = async ()=>{\n    await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n};\nLoadDB();\n// Get user profile\nasync function GET(request) {\n    try {\n        const userId = request.nextUrl.searchParams.get(\"userId\");\n        if (!userId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n                success: false,\n                message: \"User ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const user = await _lib_models_UserModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId, {\n            password: 0\n        }); // Exclude password\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n                success: false,\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n            success: true,\n            user: {\n                id: user._id,\n                email: user.email,\n                role: user.role,\n                profilePicture: user.profilePicture,\n                name: user.name\n            }\n        });\n    } catch (error) {\n        console.error(\"Profile fetch error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n            success: false,\n            message: \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Update user profile\nasync function PUT(request) {\n    try {\n        const formData = await request.formData();\n        const userId = formData.get(\"userId\");\n        if (!userId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n                success: false,\n                message: \"User ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find the user\n        const user = await _lib_models_UserModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n                success: false,\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Update name if provided\n        const name = formData.get(\"name\");\n        if (name) {\n            user.name = name;\n        }\n        // Handle profile picture upload if provided\n        const profilePicture = formData.get(\"profilePicture\");\n        if (profilePicture && profilePicture.size > 0) {\n            // Process new image\n            const imageByteData = await profilePicture.arrayBuffer();\n            const buffer = Buffer.from(imageByteData);\n            const timestamp = Date.now();\n            const filename = profilePicture.name.replace(/\\s+/g, \"_\");\n            const path = `./public/profiles/${timestamp}_${filename}`;\n            // Ensure the directory exists\n            if (!fs__WEBPACK_IMPORTED_MODULE_4___default().existsSync(\"./public/profiles\")) {\n                fs__WEBPACK_IMPORTED_MODULE_4___default().mkdirSync(\"./public/profiles\", {\n                    recursive: true\n                });\n            }\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(path, buffer);\n            const imgUrl = `/profiles/${timestamp}_${filename}`;\n            // Delete old profile picture if it exists and is not the default\n            if (user.profilePicture && user.profilePicture !== \"/default_profile.png\") {\n                try {\n                    fs__WEBPACK_IMPORTED_MODULE_4___default().unlink(`./public${user.profilePicture}`, ()=>{});\n                } catch (error) {\n                    console.error(\"Error deleting old profile picture:\", error);\n                }\n            }\n            // Update profile picture URL\n            user.profilePicture = imgUrl;\n        }\n        // Save the updated user\n        await user.save();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n            success: true,\n            message: \"Profile updated successfully\",\n            user: {\n                id: user._id,\n                email: user.email,\n                role: user.role,\n                profilePicture: user.profilePicture,\n                name: user.name\n            }\n        });\n    } catch (error) {\n        console.error(\"Profile update error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_2__[\"default\"].json({\n            success: false,\n            message: \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/profile/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/UserModel.js":
/*!*********************************!*\
  !*** ./lib/models/UserModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    password: {\n        type: String,\n        required: true\n    },\n    role: {\n        type: String,\n        default: \"user\",\n        enum: [\n            \"user\",\n            \"admin\"\n        ]\n    },\n    profilePicture: {\n        type: String,\n        default: \"/default_profile.png\"\n    },\n    name: {\n        type: String,\n        default: \"\"\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst UserModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).user || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"user\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/UserModel.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprofile%2Froute&page=%2Fapi%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();