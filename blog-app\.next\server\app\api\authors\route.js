"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/authors/route";
exports.ids = ["app/api/authors/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauthors%2Froute&page=%2Fapi%2Fauthors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauthors%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauthors%2Froute&page=%2Fapi%2Fauthors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauthors%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_authors_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/authors/route.js */ \"(rsc)/./app/api/authors/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/authors/route\",\n        pathname: \"/api/authors\",\n        filename: \"route\",\n        bundlePath: \"app/api/authors/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\authors\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_authors_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/authors/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauthors%2Froute&page=%2Fapi%2Fauthors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauthors%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/authors/route.js":
/*!**********************************!*\
  !*** ./app/api/authors/route.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _models_Author__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Author */ \"(rsc)/./models/Author.js\");\n\n\n\n// Connect to database\nasync function GET() {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const authors = await _models_Author__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({}).sort({\n            name: 1\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            authors\n        });\n    } catch (error) {\n        console.error(\"Error fetching authors:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch authors\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const formData = await request.formData();\n        const name = formData.get(\"name\");\n        const bio = formData.get(\"bio\");\n        const imageFile = formData.get(\"image\");\n        if (!name) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Author name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if author already exists\n        const existingAuthor = await _models_Author__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            name\n        });\n        if (existingAuthor) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Author already exists\"\n            }, {\n                status: 409\n            });\n        }\n        // Create author data object\n        const authorData = {\n            name,\n            bio: bio || \"\"\n        };\n        // Upload image if provided\n        if (imageFile && imageFile.size > 0) {\n            const imageByteData = await imageFile.arrayBuffer();\n            const buffer = Buffer.from(imageByteData);\n            const timestamp = Date.now();\n            const path = `./public/authors/${timestamp}_${imageFile.name}`;\n            // Ensure directory exists\n            const fs = __webpack_require__(/*! fs */ \"fs\");\n            if (!fs.existsSync(\"./public/authors\")) {\n                fs.mkdirSync(\"./public/authors\", {\n                    recursive: true\n                });\n            }\n            // Write file\n            const { writeFile } = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n            await writeFile(path, buffer);\n            // Set image path\n            authorData.image = `/authors/${timestamp}_${imageFile.name}`;\n        }\n        // Create new author\n        const newAuthor = new _models_Author__WEBPACK_IMPORTED_MODULE_2__[\"default\"](authorData);\n        await newAuthor.save();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Author added successfully\",\n            author: newAuthor\n        });\n    } catch (error) {\n        console.error(\"Error creating author:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to create author\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Author ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const deletedAuthor = await _models_Author__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findByIdAndDelete(id);\n        if (!deletedAuthor) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Author not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Author deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting author:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to delete author\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGhvcnMvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJDO0FBQ0M7QUFDUDtBQUVyQyxzQkFBc0I7QUFDZixlQUFlRztJQUNwQixJQUFJO1FBQ0YsTUFBTUYseURBQVNBO1FBQ2YsTUFBTUcsVUFBVSxNQUFNRixzREFBTUEsQ0FBQ0csSUFBSSxDQUFDLENBQUMsR0FBR0MsSUFBSSxDQUFDO1lBQUVDLE1BQU07UUFBRTtRQUVyRCxPQUFPUCxrRkFBWUEsQ0FBQ1EsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RMO1FBQ0Y7SUFDRixFQUFFLE9BQU9NLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsT0FBT1Ysa0ZBQVlBLENBQUNRLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNURyxTQUFTO1FBQ1gsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFDbkI7QUFDRjtBQUVPLGVBQWVDLEtBQUtDLE9BQU87SUFDaEMsSUFBSTtRQUNGLE1BQU1kLHlEQUFTQTtRQUVmLE1BQU1lLFdBQVcsTUFBTUQsUUFBUUMsUUFBUTtRQUN2QyxNQUFNVCxPQUFPUyxTQUFTQyxHQUFHLENBQUM7UUFDMUIsTUFBTUMsTUFBTUYsU0FBU0MsR0FBRyxDQUFDO1FBQ3pCLE1BQU1FLFlBQVlILFNBQVNDLEdBQUcsQ0FBQztRQUUvQixJQUFJLENBQUNWLE1BQU07WUFDVCxPQUFPUCxrRkFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUN2QkMsU0FBUztnQkFDVEcsU0FBUztZQUNYLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNuQjtRQUVBLGlDQUFpQztRQUNqQyxNQUFNTyxpQkFBaUIsTUFBTWxCLHNEQUFNQSxDQUFDbUIsT0FBTyxDQUFDO1lBQUVkO1FBQUs7UUFDbkQsSUFBSWEsZ0JBQWdCO1lBQ2xCLE9BQU9wQixrRkFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUN2QkMsU0FBUztnQkFDVEcsU0FBUztZQUNYLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNuQjtRQUVBLDRCQUE0QjtRQUM1QixNQUFNUyxhQUFhO1lBQ2pCZjtZQUNBVyxLQUFLQSxPQUFPO1FBQ2Q7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSUMsYUFBYUEsVUFBVUksSUFBSSxHQUFHLEdBQUc7WUFDbkMsTUFBTUMsZ0JBQWdCLE1BQU1MLFVBQVVNLFdBQVc7WUFDakQsTUFBTUMsU0FBU0MsT0FBT0MsSUFBSSxDQUFDSjtZQUMzQixNQUFNSyxZQUFZQyxLQUFLQyxHQUFHO1lBQzFCLE1BQU1DLE9BQU8sQ0FBQyxpQkFBaUIsRUFBRUgsVUFBVSxDQUFDLEVBQUVWLFVBQVVaLElBQUksQ0FBQyxDQUFDO1lBRTlELDBCQUEwQjtZQUMxQixNQUFNMEIsS0FBS0MsbUJBQU9BLENBQUM7WUFDbkIsSUFBSSxDQUFDRCxHQUFHRSxVQUFVLENBQUMscUJBQXFCO2dCQUN0Q0YsR0FBR0csU0FBUyxDQUFDLG9CQUFvQjtvQkFBRUMsV0FBVztnQkFBSztZQUNyRDtZQUVBLGFBQWE7WUFDYixNQUFNLEVBQUVDLFNBQVMsRUFBRSxHQUFHSixtQkFBT0EsQ0FBQztZQUM5QixNQUFNSSxVQUFVTixNQUFNTjtZQUV0QixpQkFBaUI7WUFDakJKLFdBQVdpQixLQUFLLEdBQUcsQ0FBQyxTQUFTLEVBQUVWLFVBQVUsQ0FBQyxFQUFFVixVQUFVWixJQUFJLENBQUMsQ0FBQztRQUM5RDtRQUVBLG9CQUFvQjtRQUNwQixNQUFNaUMsWUFBWSxJQUFJdEMsc0RBQU1BLENBQUNvQjtRQUM3QixNQUFNa0IsVUFBVUMsSUFBSTtRQUVwQixPQUFPekMsa0ZBQVlBLENBQUNRLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNURyxTQUFTO1lBQ1Q4QixRQUFRRjtRQUNWO0lBQ0YsRUFBRSxPQUFPOUIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtRQUN4QyxPQUFPVixrRkFBWUEsQ0FBQ1EsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RHLFNBQVM7UUFDWCxHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUNuQjtBQUNGO0FBRU8sZUFBZThCLE9BQU81QixPQUFPO0lBQ2xDLElBQUk7UUFDRixNQUFNZCx5REFBU0E7UUFFZixNQUFNLEVBQUUyQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJOUIsUUFBUStCLEdBQUc7UUFDNUMsTUFBTUMsS0FBS0gsYUFBYTNCLEdBQUcsQ0FBQztRQUU1QixJQUFJLENBQUM4QixJQUFJO1lBQ1AsT0FBTy9DLGtGQUFZQSxDQUFDUSxJQUFJLENBQUM7Z0JBQ3ZCQyxTQUFTO2dCQUNURyxTQUFTO1lBQ1gsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ25CO1FBRUEsTUFBTW1DLGdCQUFnQixNQUFNOUMsc0RBQU1BLENBQUMrQyxpQkFBaUIsQ0FBQ0Y7UUFFckQsSUFBSSxDQUFDQyxlQUFlO1lBQ2xCLE9BQU9oRCxrRkFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUN2QkMsU0FBUztnQkFDVEcsU0FBUztZQUNYLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNuQjtRQUVBLE9BQU9iLGtGQUFZQSxDQUFDUSxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEcsU0FBUztRQUNYO0lBQ0YsRUFBRSxPQUFPRixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU9WLGtGQUFZQSxDQUFDUSxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEcsU0FBUztRQUNYLEdBQUc7WUFBRUMsUUFBUTtRQUFJO0lBQ25CO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vYXBwL2FwaS9hdXRob3JzL3JvdXRlLmpzPzQ3M2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgQ29ubmVjdERCIH0gZnJvbSBcIkAvbGliL2NvbmZpZy9kYlwiO1xuaW1wb3J0IEF1dGhvciBmcm9tICdAL21vZGVscy9BdXRob3InO1xuXG4vLyBDb25uZWN0IHRvIGRhdGFiYXNlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIGF3YWl0IENvbm5lY3REQigpO1xuICAgIGNvbnN0IGF1dGhvcnMgPSBhd2FpdCBBdXRob3IuZmluZCh7fSkuc29ydCh7IG5hbWU6IDEgfSk7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBhdXRob3JzXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYXV0aG9yczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ0ZhaWxlZCB0byBmZXRjaCBhdXRob3JzJ1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdCkge1xuICB0cnkge1xuICAgIGF3YWl0IENvbm5lY3REQigpO1xuICAgIFxuICAgIGNvbnN0IGZvcm1EYXRhID0gYXdhaXQgcmVxdWVzdC5mb3JtRGF0YSgpO1xuICAgIGNvbnN0IG5hbWUgPSBmb3JtRGF0YS5nZXQoJ25hbWUnKTtcbiAgICBjb25zdCBiaW8gPSBmb3JtRGF0YS5nZXQoJ2JpbycpO1xuICAgIGNvbnN0IGltYWdlRmlsZSA9IGZvcm1EYXRhLmdldCgnaW1hZ2UnKTtcbiAgICBcbiAgICBpZiAoIW5hbWUpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnQXV0aG9yIG5hbWUgaXMgcmVxdWlyZWQnXG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cbiAgICBcbiAgICAvLyBDaGVjayBpZiBhdXRob3IgYWxyZWFkeSBleGlzdHNcbiAgICBjb25zdCBleGlzdGluZ0F1dGhvciA9IGF3YWl0IEF1dGhvci5maW5kT25lKHsgbmFtZSB9KTtcbiAgICBpZiAoZXhpc3RpbmdBdXRob3IpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnQXV0aG9yIGFscmVhZHkgZXhpc3RzJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwOSB9KTtcbiAgICB9XG4gICAgXG4gICAgLy8gQ3JlYXRlIGF1dGhvciBkYXRhIG9iamVjdFxuICAgIGNvbnN0IGF1dGhvckRhdGEgPSB7XG4gICAgICBuYW1lLFxuICAgICAgYmlvOiBiaW8gfHwgJydcbiAgICB9O1xuICAgIFxuICAgIC8vIFVwbG9hZCBpbWFnZSBpZiBwcm92aWRlZFxuICAgIGlmIChpbWFnZUZpbGUgJiYgaW1hZ2VGaWxlLnNpemUgPiAwKSB7XG4gICAgICBjb25zdCBpbWFnZUJ5dGVEYXRhID0gYXdhaXQgaW1hZ2VGaWxlLmFycmF5QnVmZmVyKCk7XG4gICAgICBjb25zdCBidWZmZXIgPSBCdWZmZXIuZnJvbShpbWFnZUJ5dGVEYXRhKTtcbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCk7XG4gICAgICBjb25zdCBwYXRoID0gYC4vcHVibGljL2F1dGhvcnMvJHt0aW1lc3RhbXB9XyR7aW1hZ2VGaWxlLm5hbWV9YDtcbiAgICAgIFxuICAgICAgLy8gRW5zdXJlIGRpcmVjdG9yeSBleGlzdHNcbiAgICAgIGNvbnN0IGZzID0gcmVxdWlyZSgnZnMnKTtcbiAgICAgIGlmICghZnMuZXhpc3RzU3luYygnLi9wdWJsaWMvYXV0aG9ycycpKSB7XG4gICAgICAgIGZzLm1rZGlyU3luYygnLi9wdWJsaWMvYXV0aG9ycycsIHsgcmVjdXJzaXZlOiB0cnVlIH0pO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBXcml0ZSBmaWxlXG4gICAgICBjb25zdCB7IHdyaXRlRmlsZSB9ID0gcmVxdWlyZSgnZnMvcHJvbWlzZXMnKTtcbiAgICAgIGF3YWl0IHdyaXRlRmlsZShwYXRoLCBidWZmZXIpO1xuICAgICAgXG4gICAgICAvLyBTZXQgaW1hZ2UgcGF0aFxuICAgICAgYXV0aG9yRGF0YS5pbWFnZSA9IGAvYXV0aG9ycy8ke3RpbWVzdGFtcH1fJHtpbWFnZUZpbGUubmFtZX1gO1xuICAgIH1cbiAgICBcbiAgICAvLyBDcmVhdGUgbmV3IGF1dGhvclxuICAgIGNvbnN0IG5ld0F1dGhvciA9IG5ldyBBdXRob3IoYXV0aG9yRGF0YSk7XG4gICAgYXdhaXQgbmV3QXV0aG9yLnNhdmUoKTtcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdBdXRob3IgYWRkZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgIGF1dGhvcjogbmV3QXV0aG9yXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgYXV0aG9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnRmFpbGVkIHRvIGNyZWF0ZSBhdXRob3InXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gREVMRVRFKHJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBDb25uZWN0REIoKTtcbiAgICBcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgaWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpO1xuICAgIFxuICAgIGlmICghaWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnQXV0aG9yIElEIGlzIHJlcXVpcmVkJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgZGVsZXRlZEF1dGhvciA9IGF3YWl0IEF1dGhvci5maW5kQnlJZEFuZERlbGV0ZShpZCk7XG4gICAgXG4gICAgaWYgKCFkZWxldGVkQXV0aG9yKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ0F1dGhvciBub3QgZm91bmQnXG4gICAgICB9LCB7IHN0YXR1czogNDA0IH0pO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdBdXRob3IgZGVsZXRlZCBzdWNjZXNzZnVsbHknXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgYXV0aG9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnRmFpbGVkIHRvIGRlbGV0ZSBhdXRob3InXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuXG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQ29ubmVjdERCIiwiQXV0aG9yIiwiR0VUIiwiYXV0aG9ycyIsImZpbmQiLCJzb3J0IiwibmFtZSIsImpzb24iLCJzdWNjZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsInN0YXR1cyIsIlBPU1QiLCJyZXF1ZXN0IiwiZm9ybURhdGEiLCJnZXQiLCJiaW8iLCJpbWFnZUZpbGUiLCJleGlzdGluZ0F1dGhvciIsImZpbmRPbmUiLCJhdXRob3JEYXRhIiwic2l6ZSIsImltYWdlQnl0ZURhdGEiLCJhcnJheUJ1ZmZlciIsImJ1ZmZlciIsIkJ1ZmZlciIsImZyb20iLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicGF0aCIsImZzIiwicmVxdWlyZSIsImV4aXN0c1N5bmMiLCJta2RpclN5bmMiLCJyZWN1cnNpdmUiLCJ3cml0ZUZpbGUiLCJpbWFnZSIsIm5ld0F1dGhvciIsInNhdmUiLCJhdXRob3IiLCJERUxFVEUiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJpZCIsImRlbGV0ZWRBdXRob3IiLCJmaW5kQnlJZEFuZERlbGV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/authors/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./models/Author.js":
/*!**************************!*\
  !*** ./models/Author.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AuthorSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Author name is required\"\n        ],\n        unique: true,\n        trim: true\n    },\n    image: {\n        type: String,\n        default: \"/author_img.png\"\n    },\n    bio: {\n        type: String,\n        default: \"\"\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Author || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Author\", AuthorSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9tb2RlbHMvQXV0aG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUVoQyxNQUFNQyxlQUFlLElBQUlELHdEQUFlLENBQUM7SUFDdkNHLE1BQU07UUFDSkMsTUFBTUM7UUFDTkMsVUFBVTtZQUFDO1lBQU07U0FBMEI7UUFDM0NDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0lBQ0FDLE9BQU87UUFDTEwsTUFBTUM7UUFDTkssU0FBUztJQUNYO0lBQ0FDLEtBQUs7UUFDSFAsTUFBTUM7UUFDTkssU0FBUztJQUNYO0lBQ0FFLFdBQVc7UUFDVFIsTUFBTVM7UUFDTkgsU0FBU0csS0FBS0MsR0FBRztJQUNuQjtBQUNGO0FBRUEsaUVBQWVkLHdEQUFlLENBQUNnQixNQUFNLElBQUloQixxREFBYyxDQUFDLFVBQVVDLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vbW9kZWxzL0F1dGhvci5qcz81ZWZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb25nb29zZSBmcm9tICdtb25nb29zZSc7XG5cbmNvbnN0IEF1dGhvclNjaGVtYSA9IG5ldyBtb25nb29zZS5TY2hlbWEoe1xuICBuYW1lOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ0F1dGhvciBuYW1lIGlzIHJlcXVpcmVkJ10sXG4gICAgdW5pcXVlOiB0cnVlLFxuICAgIHRyaW06IHRydWVcbiAgfSxcbiAgaW1hZ2U6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZGVmYXVsdDogJy9hdXRob3JfaW1nLnBuZydcbiAgfSxcbiAgYmlvOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGRlZmF1bHQ6ICcnXG4gIH0sXG4gIGNyZWF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3dcbiAgfVxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IG1vbmdvb3NlLm1vZGVscy5BdXRob3IgfHwgbW9uZ29vc2UubW9kZWwoJ0F1dGhvcicsIEF1dGhvclNjaGVtYSk7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiQXV0aG9yU2NoZW1hIiwiU2NoZW1hIiwibmFtZSIsInR5cGUiLCJTdHJpbmciLCJyZXF1aXJlZCIsInVuaXF1ZSIsInRyaW0iLCJpbWFnZSIsImRlZmF1bHQiLCJiaW8iLCJjcmVhdGVkQXQiLCJEYXRlIiwibm93IiwibW9kZWxzIiwiQXV0aG9yIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./models/Author.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauthors%2Froute&page=%2Fapi%2Fauthors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauthors%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();