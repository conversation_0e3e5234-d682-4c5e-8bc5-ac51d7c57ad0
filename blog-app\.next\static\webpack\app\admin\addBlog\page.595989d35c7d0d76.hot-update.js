"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/addBlog/page",{

/***/ "(app-pages-browser)/./app/admin/addBlog/page.jsx":
/*!************************************!*\
  !*** ./app/admin/addBlog/page.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Components/AdminComponents/BlogTableItem */ \"(app-pages-browser)/./Components/AdminComponents/BlogTableItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BlogManagementPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"add\"); // 'add' or 'manage'\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"\",\n        commentsEnabled: true\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [tempBlogId, setTempBlogId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        fetchCategories();\n        fetchAuthors();\n        if (activeTab === \"manage\") {\n            fetchBlogs();\n        }\n    }, [\n        activeTab\n    ]);\n    // Generate a temporary blog ID for image uploads when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n    }, []);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n                setData((prev)=>({\n                        ...prev,\n                        category: response.data.categories[0].name\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No categories found. Please add categories in Settings.\");\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load categories\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n                setData((prev)=>({\n                        ...prev,\n                        author: response.data.authors[0].name,\n                        authorId: response.data.authors[0]._id,\n                        authorImg: response.data.authors[0].image || \"/author_img.png\"\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No authors found. Please add authors in Settings.\");\n                setAuthors([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load authors\");\n            setAuthors([]);\n        }\n    };\n    const fetchBlogs = async ()=>{\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load blogs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const onChangeHandler = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"authorId\") {\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData({\n                    ...data,\n                    author: selectedAuthor.name,\n                    authorId: selectedAuthor._id,\n                    authorImg: selectedAuthor.image || \"/author_img.png\"\n                });\n            }\n        } else {\n            setData({\n                ...data,\n                [name]: value\n            });\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        if (!image) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select a blog thumbnail image\");\n            return;\n        }\n        if (!data.title.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter a blog title\");\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"title\", data.title);\n        formData.append(\"description\", data.description);\n        formData.append(\"category\", data.category);\n        formData.append(\"author\", data.author);\n        formData.append(\"authorId\", data.authorId);\n        formData.append(\"authorImg\", data.authorImg);\n        formData.append(\"commentsEnabled\", data.commentsEnabled);\n        formData.append(\"image\", image);\n        formData.append(\"tempBlogId\", tempBlogId);\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog added successfully\");\n                setImage(false);\n                setData({\n                    title: \"\",\n                    description: \"\",\n                    category: categories.length > 0 ? categories[0].name : \"\",\n                    author: authors.length > 0 ? authors[0].name : \"\",\n                    authorId: authors.length > 0 ? authors[0]._id : \"\",\n                    authorImg: authors.length > 0 ? authors[0].image || \"/author_img.png\" : \"/author_img.png\",\n                    commentsEnabled: true\n                });\n                // Reset temp blog ID and clear images\n                setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n                setAllImages([]);\n                // Switch to manage tab and refresh blog list\n                setActiveTab(\"manage\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.msg || \"Error adding blog\");\n            }\n        } catch (error) {\n            console.error(\"Error submitting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteBlog = async (mongoId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this blog?\")) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/blog\", {\n                params: {\n                    id: mongoId\n                }\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog deleted successfully\");\n            fetchBlogs();\n        } catch (error) {\n            console.error(\"Error deleting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    const insertBlogMention = (blogId, blogTitle)=>{\n        const mention = \"[[\".concat(blogId, \"|\").concat(blogTitle, \"]]\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        setShowBlogSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        if (!tempBlogId) return;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: tempBlogId,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", tempBlogId || \"new\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/images/\".concat(imageId));\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = \"{{image:\".concat(imageUrl, \"|\").concat(filename, \"}}\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Blog Management\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 327,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex border-b border-gray-300 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg \".concat(activeTab === \"add\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"add\"),\n                        children: \"Add New Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 331,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg ml-2 \".concat(activeTab === \"manage\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"manage\"),\n                        children: \"Manage Blogs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 341,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 330,\n                columnNumber: 13\n            }, undefined),\n            activeTab === \"add\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onSubmitHandler,\n                className: \"max-w-[800px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Create New Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 357,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Upload Thumbnail \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 86\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"image\",\n                                    className: \"cursor-pointer block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        className: \"border border-gray-300 rounded-md\",\n                                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                                        width: 200,\n                                        height: 120,\n                                        alt: \"\",\n                                        style: {\n                                            objectFit: \"cover\",\n                                            height: \"120px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    onChange: (e)=>setImage(e.target.files[0]),\n                                    type: \"file\",\n                                    id: \"image\",\n                                    hidden: true,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 359,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Blog Title \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    name: \"title\",\n                                    onChange: onChangeHandler,\n                                    value: data.title,\n                                    className: \"w-full px-4 py-3 border rounded-md\",\n                                    type: \"text\",\n                                    placeholder: \"Type here\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 374,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"blog-description\",\n                                        name: \"description\",\n                                        onChange: onChangeHandler,\n                                        value: data.description,\n                                        className: \"w-full px-4 py-3 border rounded-md\",\n                                        placeholder: \"Write content here\",\n                                        rows: 6,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center flex-wrap gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllBlogs();\n                                                setShowBlogSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Mention another blog\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllImages();\n                                                setShowImageSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Insert image\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Formats: [[blogId|blogTitle]] | \",\n                                                    \"{{image:url|filename}}\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 29\n                                }, undefined),\n                                showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Select a blog to mention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowBlogSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search blogs...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y\",\n                                                children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                        onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 relative mr-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: blog.image,\n                                                                    alt: blog.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: blog.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: blog.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, blog._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 33\n                                }, undefined),\n                                showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Insert Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowImageSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"Upload New Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: handleImageUpload,\n                                                                disabled: !selectedImageFile || imageUploadLoading,\n                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                                children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search images...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: imageSearchTerm,\n                                                onChange: (e)=>setImageSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                                children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative mb-2\",\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        src: image.url,\n                                                                        alt: image.filename,\n                                                                        fill: true,\n                                                                        className: \"object-cover rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            deleteImage(image._id, image.url);\n                                                                        },\n                                                                        className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        title: \"Delete image\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 65\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 truncate\",\n                                                                        children: image.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: new Date(image.uploadDate).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, image._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No images found. Upload your first image above.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 387,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading categories...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 33\n                                }, undefined) : categories.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    onChange: onChangeHandler,\n                                    value: data.category,\n                                    className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                    required: true,\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.name,\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No categories available. Please add categories in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 584,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading authors...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 33\n                                }, undefined) : authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"authorId\",\n                                            onChange: onChangeHandler,\n                                            value: data.authorId,\n                                            className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                            required: true,\n                                            children: authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: author._id,\n                                                    children: author.name\n                                                }, author._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 45\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: data.authorImg,\n                                                alt: data.author,\n                                                className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No authors available. Please add authors in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 607,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center gap-3 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            name: \"commentsEnabled\",\n                                            checked: data.commentsEnabled,\n                                            onChange: (e)=>setData((prev)=>({\n                                                        ...prev,\n                                                        commentsEnabled: e.target.checked\n                                                    })),\n                                            className: \"w-4 h-4 text-black border-gray-300 rounded focus:ring-black\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Enable comments for this blog post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1 ml-7\",\n                                    children: \"When enabled, readers can comment on this blog post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 642,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors\",\n                            disabled: loading || categories.length === 0 || authors.length === 0,\n                            children: loading ? \"Creating...\" : \"Create Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 660,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                    lineNumber: 356,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 355,\n                columnNumber: 17\n            }, undefined),\n            activeTab === \"manage\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[850px] bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Manage Blog Posts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 674,\n                        columnNumber: 21\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 678,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: \"Loading blogs...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 679,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 677,\n                        columnNumber: 25\n                    }, undefined) : blogs.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative overflow-x-auto border border-gray-300 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm text-gray-500 table-fixed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Author\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: blogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            mongoId: item._id,\n                                            title: item.title,\n                                            author: item.author,\n                                            authorImg: item.authorImg,\n                                            date: item.date,\n                                            deleteBlog: deleteBlog\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 683,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 682,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No blogs found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 717,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"add\"),\n                                className: \"mt-4 text-blue-600 hover:underline\",\n                                children: \"Add your first blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 718,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 716,\n                        columnNumber: 25\n                    }, undefined),\n                    blogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-sm text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Showing \",\n                                blogs.length,\n                                \" blog posts\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 730,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 729,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 673,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n        lineNumber: 326,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BlogManagementPage, \"wnAKy5iwsa+kpMyQuoXKgho6Us0=\");\n_c = BlogManagementPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogManagementPage);\nvar _c;\n$RefreshReg$(_c, \"BlogManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/addBlog/page.jsx\n"));

/***/ })

});