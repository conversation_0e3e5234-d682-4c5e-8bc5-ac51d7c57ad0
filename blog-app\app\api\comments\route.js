import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import { verifyToken } from '@/lib/utils/token';
import CommentModel from '@/lib/models/CommentModel';
import CommentReactionModel from '@/lib/models/CommentReactionModel';
import CommentSettingsModel from '@/lib/models/CommentSettingsModel';
import UserModel from '@/lib/models/UserModel';

// Helper function to get user from token
function getUserFromToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.split(' ')[1];
  return verifyToken(token);
}

// Helper function to check if comments are enabled
async function checkCommentsEnabled(blogId = null) {
  try {
    // First check the blog's own commentsEnabled field
    if (blogId) {
      const BlogModel = (await import('@/lib/models/BlogModel')).default;
      const blog = await BlogModel.findById(blogId);
      if (blog && blog.commentsEnabled === false) {
        return false; // Blog has comments disabled
      }
    }

    // Then check blog-specific comment settings, then global settings
    let settings = null;
    if (blogId) {
      settings = await CommentSettingsModel.findOne({ blogId });
    }
    if (!settings) {
      settings = await CommentSettingsModel.findOne({ blogId: null });
    }

    return settings ? settings.commentsEnabled : true; // Default to enabled
  } catch (error) {
    console.error('Error checking comment settings:', error);
    return true; // Default to enabled on error
  }
}

// GET - Fetch comments for a blog
export async function GET(request) {
  try {
    await ConnectDB();
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('blogId');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    
    if (!blogId) {
      return NextResponse.json({ 
        success: false, 
        message: "Blog ID is required" 
      }, { status: 400 });
    }

    // Check if comments are enabled
    const commentsEnabled = await checkCommentsEnabled(blogId);
    if (!commentsEnabled) {
      return NextResponse.json({ 
        success: false, 
        message: "Comments are disabled for this blog" 
      }, { status: 403 });
    }

    const skip = (page - 1) * limit;

    // Fetch top-level comments (no parent) with user info
    const comments = await CommentModel.find({ 
      blogId, 
      parentCommentId: null,
      isDeleted: false 
    })
    .populate('userId', 'name email profilePicture')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

    // For each comment, fetch its replies and reaction counts
    const commentsWithDetails = await Promise.all(
      comments.map(async (comment) => {
        // Fetch replies
        const replies = await CommentModel.find({ 
          parentCommentId: comment._id,
          isDeleted: false 
        })
        .populate('userId', 'name email profilePicture')
        .sort({ createdAt: 1 });

        // Fetch reaction counts
        const likeCount = await CommentReactionModel.countDocuments({ 
          commentId: comment._id, 
          reactionType: 'like' 
        });
        const dislikeCount = await CommentReactionModel.countDocuments({ 
          commentId: comment._id, 
          reactionType: 'dislike' 
        });

        // Add reaction counts to replies as well
        const repliesWithReactions = await Promise.all(
          replies.map(async (reply) => {
            const replyLikeCount = await CommentReactionModel.countDocuments({ 
              commentId: reply._id, 
              reactionType: 'like' 
            });
            const replyDislikeCount = await CommentReactionModel.countDocuments({ 
              commentId: reply._id, 
              reactionType: 'dislike' 
            });
            
            return {
              ...reply.toObject(),
              likeCount: replyLikeCount,
              dislikeCount: replyDislikeCount
            };
          })
        );

        return {
          ...comment.toObject(),
          replies: repliesWithReactions,
          likeCount,
          dislikeCount
        };
      })
    );

    // Get total count for pagination
    const totalComments = await CommentModel.countDocuments({ 
      blogId, 
      parentCommentId: null,
      isDeleted: false 
    });

    return NextResponse.json({ 
      success: true, 
      comments: commentsWithDetails,
      pagination: {
        page,
        limit,
        total: totalComments,
        pages: Math.ceil(totalComments / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch comments" 
    }, { status: 500 });
  }
}

// POST - Create a new comment
export async function POST(request) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ 
        success: false, 
        message: "Authentication required" 
      }, { status: 401 });
    }

    const body = await request.json();
    const { blogId, content, parentCommentId = null } = body;

    if (!blogId || !content) {
      return NextResponse.json({ 
        success: false, 
        message: "Blog ID and content are required" 
      }, { status: 400 });
    }

    // Check if comments are enabled
    const commentsEnabled = await checkCommentsEnabled(blogId);
    if (!commentsEnabled) {
      return NextResponse.json({ 
        success: false, 
        message: "Comments are disabled for this blog" 
      }, { status: 403 });
    }

    // Validate content length
    if (content.trim().length === 0 || content.length > 1000) {
      return NextResponse.json({ 
        success: false, 
        message: "Comment must be between 1 and 1000 characters" 
      }, { status: 400 });
    }

    // If this is a reply, verify the parent comment exists
    if (parentCommentId) {
      const parentComment = await CommentModel.findById(parentCommentId);
      if (!parentComment || parentComment.isDeleted) {
        return NextResponse.json({ 
          success: false, 
          message: "Parent comment not found" 
        }, { status: 404 });
      }
    }

    // Create the comment
    const newComment = await CommentModel.create({
      blogId,
      userId: userData.userId,
      content: content.trim(),
      parentCommentId
    });

    // Populate user info for response
    await newComment.populate('userId', 'name email profilePicture');

    return NextResponse.json({ 
      success: true, 
      message: "Comment created successfully",
      comment: {
        ...newComment.toObject(),
        likeCount: 0,
        dislikeCount: 0,
        replies: []
      }
    });
  } catch (error) {
    console.error("Error creating comment:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to create comment" 
    }, { status: 500 });
  }
}
