import { NextResponse } from 'next/server'
import { ConnectDB } from '@/lib/config/db'
import Author from '@/models/Author'

// GET single author
export async function GET(request, { params }) {
  try {
    await ConnectDB()
    
    const author = await Author.findById(params.id)
    
    if (!author) {
      return NextResponse.json({ 
        success: false, 
        message: 'Author not found' 
      }, { status: 404 })
    }
    
    return NextResponse.json({ 
      success: true, 
      author 
    })
  } catch (error) {
    console.error('Error fetching author:', error)
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to fetch author' 
    }, { status: 500 })
  }
}

// UPDATE author
export async function PUT(request, { params }) {
  try {
    await ConnectDB()
    
    const formData = await request.formData()
    const name = formData.get('name')
    const bio = formData.get('bio')
    const imageFile = formData.get('image')
    
    if (!name) {
      return NextResponse.json({ 
        success: false, 
        message: 'Author name is required' 
      }, { status: 400 })
    }
    
    // Find the author
    const author = await Author.findById(params.id)
    
    if (!author) {
      return NextResponse.json({ 
        success: false, 
        message: 'Author not found' 
      }, { status: 404 })
    }
    
    // Update fields
    author.name = name
    author.bio = bio
    
    // Upload new image if provided
    if (imageFile && imageFile.size > 0) {
      const imageByteData = await imageFile.arrayBuffer();
      const buffer = Buffer.from(imageByteData);
      const timestamp = Date.now();
      const path = `./public/authors/${timestamp}_${imageFile.name}`;
      
      // Ensure directory exists
      const fs = require('fs');
      if (!fs.existsSync('./public/authors')) {
        fs.mkdirSync('./public/authors', { recursive: true });
      }
      
      // Write file
      const { writeFile } = require('fs/promises');
      await writeFile(path, buffer);
      
      // Set image path
      author.image = `/authors/${timestamp}_${imageFile.name}`;
    }
    
    await author.save()
    
    return NextResponse.json({ 
      success: true, 
      message: 'Author updated successfully',
      author
    })
  } catch (error) {
    console.error('Error updating author:', error)
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to update author' 
    }, { status: 500 })
  }
}
