import Image from 'next/image';
import { assets } from '@/Assets/assets';

const TestImage = () => {
  return (
    <div className="p-4 border border-gray-300 rounded-md">
      <h2 className="text-lg font-bold mb-4">Image Test</h2>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="mb-2">From assets.profile_pic:</p>
          <Image 
            src={assets.profile_pic} 
            width={100} 
            height={100} 
            alt="Profile from assets" 
            className="rounded-full"
          />
        </div>
        
        <div>
          <p className="mb-2">From public path:</p>
          <Image 
            src="/profiles/default_profile.png" 
            width={100} 
            height={100} 
            alt="Profile from public" 
            className="rounded-full"
            onError={(e) => {
              console.error("Image failed to load");
              e.target.src = assets.profile_pic.src;
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TestImage;