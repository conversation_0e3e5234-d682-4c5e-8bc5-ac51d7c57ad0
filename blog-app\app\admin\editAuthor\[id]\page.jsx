'use client'
import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import axios from 'axios'
import { toast } from 'react-toastify'
import Image from 'next/image'
import <PERSON>ropper from 'react-easy-crop'

const EditAuthorPage = ({ params }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [author, setAuthor] = useState({
    name: '',
    bio: '',
    image: null
  })
  const [imagePreview, setImagePreview] = useState(null)
  const [newImage, setNewImage] = useState(null)
  
  // Cropper state
  const [showCropper, setShowCropper] = useState(false)
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null)
  const [croppedImage, setCroppedImage] = useState(null)

  useEffect(() => {
    const fetchAuthor = async () => {
      try {
        const response = await axios.get(`/api/authors/${params.id}`)
        if (response.data.success) {
          const authorData = response.data.author
          setAuthor({
            name: authorData.name,
            bio: authorData.bio || '',
          })
          setImagePreview(authorData.image || '/author_img.png')
        } else {
          toast.error('Failed to load author data')
          router.push('/admin/settings')
        }
      } catch (error) {
        console.error('Error fetching author:', error)
        toast.error('Failed to load author data')
        router.push('/admin/settings')
      } finally {
        setLoading(false)
      }
    }

    fetchAuthor()
  }, [params.id, router])

  const handleChange = (e) => {
    const { name, value } = e.target
    setAuthor(prev => ({ ...prev, [name]: value }))
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setNewImage(reader.result)
        setShowCropper(true)
      }
      reader.readAsDataURL(file)
    }
  }

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels)
  }, [])

  const createImage = (url) =>
    new Promise((resolve, reject) => {
      const image = new Image()
      image.addEventListener('load', () => resolve(image))
      image.addEventListener('error', (error) => reject(error))
      image.src = url
    })

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = await createImage(imageSrc)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    const maxSize = Math.max(image.width, image.height)
    const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2))

    canvas.width = safeArea
    canvas.height = safeArea

    ctx.drawImage(
      image,
      safeArea / 2 - image.width * 0.5,
      safeArea / 2 - image.height * 0.5
    )
    const data = ctx.getImageData(0, 0, safeArea, safeArea)

    canvas.width = pixelCrop.width
    canvas.height = pixelCrop.height

    ctx.putImageData(
      data,
      Math.round(0 - safeArea / 2 + image.width * 0.5 - pixelCrop.x),
      Math.round(0 - safeArea / 2 + image.height * 0.5 - pixelCrop.y)
    )

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob)
      }, 'image/jpeg')
    })
  }

  const applyCrop = async () => {
    try {
      if (!croppedAreaPixels) return
      
      const croppedBlob = await getCroppedImg(
        newImage,
        croppedAreaPixels
      )
      
      const croppedImageUrl = URL.createObjectURL(croppedBlob)
      setCroppedImage(croppedBlob)
      setImagePreview(croppedImageUrl)
      setShowCropper(false)
    } catch (e) {
      console.error('Error applying crop:', e)
      toast.error('Failed to crop image')
    }
  }

  const cancelCrop = () => {
    setShowCropper(false)
    setNewImage(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!author.name.trim()) {
      toast.error('Author name is required')
      return
    }
    
    try {
      setLoading(true)
      const formData = new FormData()
      formData.append('name', author.name)
      formData.append('bio', author.bio)
      
      if (croppedImage) {
        // Create a file from the blob
        const file = new File([croppedImage], "cropped_image.jpg", { type: "image/jpeg" })
        formData.append('image', file)
      }
      
      const response = await axios.put(`/api/authors/${params.id}`, formData)
      
      if (response.data.success) {
        toast.success('Author updated successfully')
        router.push('/admin/settings')
      } else {
        toast.error(response.data.message || 'Failed to update author')
      }
    } catch (error) {
      console.error('Error updating author:', error)
      toast.error(error.response?.data?.message || 'Failed to update author')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
        <p>Loading author data...</p>
      </div>
    )
  }

  return (
    <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>Edit Author</h1>
      
      <div className='max-w-2xl bg-white p-6 rounded-lg border border-gray-300 shadow-md'>
        {showCropper ? (
          <div className='mb-6'>
            <h2 className='text-lg font-semibold mb-4'>Adjust Profile Image</h2>
            <div className='relative h-80 mb-4'>
              <Cropper
                image={newImage}
                crop={crop}
                zoom={zoom}
                aspect={1}
                cropShape="round"
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
              />
            </div>
            <div className='mb-4'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Zoom: {zoom.toFixed(1)}x
              </label>
              <input
                type='range'
                min={1}
                max={3}
                step={0.1}
                value={zoom}
                onChange={(e) => setZoom(parseFloat(e.target.value))}
                className='w-full'
              />
            </div>
            <div className='flex gap-3'>
              <button
                type='button'
                onClick={applyCrop}
                className='px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800'
              >
                Apply
              </button>
              <button
                type='button'
                onClick={cancelCrop}
                className='px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100'
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className='mb-4'>
              <label className='block text-gray-700 text-sm font-bold mb-2'>
                Author Name
              </label>
              <input
                type='text'
                name='name'
                value={author.name}
                onChange={handleChange}
                placeholder='Enter author name'
                className='w-full px-4 py-2 border border-gray-300 rounded-md'
                required
              />
            </div>
            
            <div className='mb-4'>
              <label className='block text-gray-700 text-sm font-bold mb-2'>
                Author Bio
              </label>
              <textarea
                name='bio'
                value={author.bio}
                onChange={handleChange}
                placeholder='Enter author bio'
                className='w-full px-4 py-2 border border-gray-300 rounded-md'
                rows={3}
              />
            </div>
            
            <div className='mb-4'>
              <label className='block text-gray-700 text-sm font-bold mb-2'>
                Author Image
              </label>
              <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
                {imagePreview && (
                  <div>
                    <img 
                      src={imagePreview} 
                      alt="Author preview" 
                      className='w-24 h-24 object-cover rounded-full border-2 border-gray-200'
                    />
                  </div>
                )}
                <div className='flex-1'>
                  <input
                    type='file'
                    accept='image/*'
                    onChange={handleImageChange}
                    className='w-full px-4 py-2 border border-gray-300 rounded-md'
                  />
                  <p className='mt-1 text-sm text-gray-500'>
                    Select an image to upload and adjust
                  </p>
                </div>
              </div>
            </div>
            
            <div className='flex gap-4'>
              <button
                type='submit'
                className='px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800'
                disabled={loading}
              >
                Update Author
              </button>
              
              <button
                type='button'
                onClick={() => router.push('/admin/settings')}
                className='px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100'
              >
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}

export default EditAuthorPage
