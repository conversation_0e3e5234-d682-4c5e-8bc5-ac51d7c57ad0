'use client'
import { assets } from "@/Assets/assets";
import Sidebar from "@/Components/AdminComponents/Sidebar";
import Image from "next/image";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Link from 'next/link';

export default function Layout({ children }) {
    const router = useRouter();
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [userProfilePicture, setUserProfilePicture] = useState("/default_profile.png");
    const [userName, setUserName] = useState("Admin");

    useEffect(() => {
        // Check if user is authenticated and has admin role
        const authToken = localStorage.getItem('authToken');
        const userRole = localStorage.getItem('userRole');
        const profilePicture = localStorage.getItem('userProfilePicture');
        const storedUserName = localStorage.getItem('userName');
        
        if (!authToken || userRole !== 'admin') {
            toast.error("You must be logged in as an admin to access this page");
            router.push('/');
        } else {
            setIsAuthenticated(true);
            if (profilePicture) {
                setUserProfilePicture(profilePicture);
            }
            if (storedUserName) {
                setUserName(storedUserName);
            }
        }
        
        setIsLoading(false);
    }, [router]);

    useEffect(() => {
        // Listen for storage events to update profile picture when changed
        const handleStorageChange = () => {
            const profilePicture = localStorage.getItem('userProfilePicture');
            const storedUserName = localStorage.getItem('userName');
            
            if (profilePicture) {
                setUserProfilePicture(profilePicture);
            }
            
            if (storedUserName) {
                setUserName(storedUserName);
            }
        };
        
        // Listen for custom profile update event
        const handleProfileUpdate = (event) => {
            const { name, profilePicture } = event.detail;
            
            if (name) {
                setUserName(name);
            }
            
            if (profilePicture) {
                setUserProfilePicture(profilePicture);
            }
        };
        
        window.addEventListener('storage', handleStorageChange);
        window.addEventListener('profileUpdate', handleProfileUpdate);
        
        // Also check on mount
        const profilePicture = localStorage.getItem('userProfilePicture');
        const storedUserName = localStorage.getItem('userName');
        
        if (profilePicture) {
            setUserProfilePicture(profilePicture);
        }
        
        if (storedUserName) {
            setUserName(storedUserName);
        }
        
        return () => {
            window.removeEventListener('storage', handleStorageChange);
            window.removeEventListener('profileUpdate', handleProfileUpdate);
        };
    }, []);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <p>Loading...</p>
            </div>
        );
    }

    if (!isAuthenticated) {
        return null; // Don't render anything while redirecting
    }

    return (
        <>
            <div className="flex">
                <ToastContainer theme="dark"/>
                <Sidebar />
                <div className="flex flex-col w-full">
                    <div className="flex items-center justify-between w-full py-3 max-h-[60px] px-12 border-b border-black">
                        <h3 className="font-medium">Admin Panel</h3>
                        <div className="flex items-center gap-4">
                            <Link 
                                href="/admin/profile" 
                                className="flex items-center gap-2 text-sm py-1 px-3 border border-black shadow-[-3px_3px_0px_#000000] hover:bg-gray-100"
                            >
                                <span>{userName}</span>
                                <Image 
                                    src={userProfilePicture} 
                                    width={24} 
                                    height={24} 
                                    alt="Profile" 
                                    className="rounded-full object-cover w-6 h-6"
                                />
                            </Link>
                        </div>
                    </div>
                    {children}
                </div>
            </div>
        </>
    )
}
