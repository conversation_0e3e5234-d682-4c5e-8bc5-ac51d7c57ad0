import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import LikeModel from '@/models/Like';

export async function GET(request) {
  try {
    await ConnectDB();
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('id');
    
    if (!blogId) {
      return NextResponse.json({ 
        success: false, 
        message: "Blog ID is required" 
      }, { status: 400 });
    }
    
    const count = await LikeModel.countDocuments({ blogId });
    
    return NextResponse.json({ 
      success: true, 
      count 
    });
  } catch (error) {
    console.error("Error fetching likes count:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch likes count" 
    }, { status: 500 });
  }
}
