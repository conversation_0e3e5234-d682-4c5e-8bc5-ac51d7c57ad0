import { ConnectDB } from "@/lib/config/db";
import UserModel from "@/lib/models/UserModel";
import { NextResponse } from "next/server";

const LoadDB = async () => {
  await ConnectDB();
}

LoadDB();

export async function GET() {
  try {
    const users = await UserModel.find({}, { password: 0 }); // Exclude passwords
    return NextResponse.json({ success: true, users });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// Delete a user
export async function DELETE(request) {
  try {
    const id = request.nextUrl.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        success: false,
        message: "User ID is required"
      }, { status: 400 });
    }
    
    // Check if user exists
    const user = await UserModel.findById(id);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: "User not found"
      }, { status: 404 });
    }
    
    // Prevent deleting the last admin
    if (user.role === 'admin') {
      const adminCount = await UserModel.countDocuments({ role: 'admin' });
      if (adminCount <= 1) {
        return NextResponse.json({
          success: false,
          message: "Cannot delete the last admin account"
        }, { status: 403 });
      }
    }
    
    // Delete the user
    await UserModel.findByIdAndDelete(id);
    
    // Return updated user list
    const users = await UserModel.find({}, { password: 0 });
    
    return NextResponse.json({
      success: true,
      message: "User deleted successfully",
      users
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to delete user"
    }, { status: 500 });
  }
}
