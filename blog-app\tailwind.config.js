/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './Components/**/*.{js,ts,jsx,tsx,mdx}',  // Note the capital C in Components
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './Assets/**/*.{js,ts,jsx,tsx}',  // Include Assets folder
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
  // Ensure Tailwind doesn't purge too aggressively in production
  safelist: [
    'animate-fade-in',
    'scrollbar-hide',
    'blog-content',
  ]
}

