import { ConnectDB } from "@/lib/config/db";
import UserModel from "@/lib/models/UserModel";
import { NextResponse } from "next/server";

// Initialize database connection
const LoadDB = async () => {
    await ConnectDB();
}
LoadDB();

export async function POST(request) {
    try {
        const { email, password, role } = await request.json();
        
        // Check if user already exists
        const existingUser = await UserModel.findOne({ email });
        if (existingUser) {
            return NextResponse.json({
                success: false,
                message: "User with this email already exists"
            }, { status: 400 });
        }
        
        // Create new user with specified role (or default to 'user')
        const newUser = await UserModel.create({
            email,
            password,
            role: role || 'user' // Use provided role or default to 'user'
        });
        
        return NextResponse.json({
            success: true,
            message: "Registration successful",
            user: {
                id: newUser._id,
                email: newUser.email,
                role: newUser.role
            }
        });
    } catch (error) {
        console.error("Registration error:", error);
        return NextResponse.json({
            success: false,
            message: "Server error"
        }, { status: 500 });
    }
}

