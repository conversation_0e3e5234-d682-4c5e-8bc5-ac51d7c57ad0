import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import { verifyToken } from '@/lib/utils/token';
import CommentSettingsModel from '@/lib/models/CommentSettingsModel';

// Helper function to get user from token
function getUserFromToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.split(' ')[1];
  return verifyToken(token);
}

// Helper function to check if user is admin
function isAdmin(userData) {
  return userData && userData.role === 'admin';
}

// GET - Fetch comment settings
export async function GET(request) {
  try {
    await ConnectDB();
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('blogId');

    let settings;
    if (blogId) {
      // Get blog-specific settings
      settings = await CommentSettingsModel.findOne({ blogId });
      if (!settings) {
        // Fall back to global settings
        settings = await CommentSettingsModel.findOne({ blogId: null });
      }
    } else {
      // Get global settings
      settings = await CommentSettingsModel.findOne({ blogId: null });
    }

    // If no settings exist, return defaults
    if (!settings) {
      return NextResponse.json({ 
        success: true,
        settings: {
          commentsEnabled: true,
          requireLogin: true,
          allowReplies: true,
          allowReactions: true,
          maxCommentLength: 1000,
          moderationEnabled: false
        }
      });
    }

    return NextResponse.json({ 
      success: true,
      settings: {
        commentsEnabled: settings.commentsEnabled,
        requireLogin: settings.requireLogin,
        allowReplies: settings.allowReplies,
        allowReactions: settings.allowReactions,
        maxCommentLength: settings.maxCommentLength,
        moderationEnabled: settings.moderationEnabled,
        blogId: settings.blogId,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error("Error fetching comment settings:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch comment settings" 
    }, { status: 500 });
  }
}

// POST - Update comment settings (Admin only)
export async function POST(request) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData || !isAdmin(userData)) {
      return NextResponse.json({ 
        success: false, 
        message: "Admin access required" 
      }, { status: 403 });
    }

    const body = await request.json();
    const { 
      blogId = null,
      commentsEnabled,
      requireLogin,
      allowReplies,
      allowReactions,
      maxCommentLength,
      moderationEnabled
    } = body;

    // Validate maxCommentLength
    if (maxCommentLength && (maxCommentLength < 100 || maxCommentLength > 5000)) {
      return NextResponse.json({ 
        success: false, 
        message: "Max comment length must be between 100 and 5000 characters" 
      }, { status: 400 });
    }

    // Find existing settings or create new ones
    let settings = await CommentSettingsModel.findOne({ blogId });
    
    if (settings) {
      // Update existing settings
      if (commentsEnabled !== undefined) settings.commentsEnabled = commentsEnabled;
      if (requireLogin !== undefined) settings.requireLogin = requireLogin;
      if (allowReplies !== undefined) settings.allowReplies = allowReplies;
      if (allowReactions !== undefined) settings.allowReactions = allowReactions;
      if (maxCommentLength !== undefined) settings.maxCommentLength = maxCommentLength;
      if (moderationEnabled !== undefined) settings.moderationEnabled = moderationEnabled;
      settings.updatedBy = userData.userId;
      
      await settings.save();
    } else {
      // Create new settings
      settings = await CommentSettingsModel.create({
        blogId,
        commentsEnabled: commentsEnabled !== undefined ? commentsEnabled : true,
        requireLogin: requireLogin !== undefined ? requireLogin : true,
        allowReplies: allowReplies !== undefined ? allowReplies : true,
        allowReactions: allowReactions !== undefined ? allowReactions : true,
        maxCommentLength: maxCommentLength || 1000,
        moderationEnabled: moderationEnabled !== undefined ? moderationEnabled : false,
        updatedBy: userData.userId
      });
    }

    return NextResponse.json({ 
      success: true, 
      message: "Comment settings updated successfully",
      settings: {
        commentsEnabled: settings.commentsEnabled,
        requireLogin: settings.requireLogin,
        allowReplies: settings.allowReplies,
        allowReactions: settings.allowReactions,
        maxCommentLength: settings.maxCommentLength,
        moderationEnabled: settings.moderationEnabled,
        blogId: settings.blogId,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error("Error updating comment settings:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to update comment settings" 
    }, { status: 500 });
  }
}
