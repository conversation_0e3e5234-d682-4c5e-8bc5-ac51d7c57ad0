"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/comments/[id]/react/route";
exports.ids = ["app/api/comments/[id]/react/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&page=%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&page=%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_id_react_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/comments/[id]/react/route.js */ \"(rsc)/./app/api/comments/[id]/react/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/comments/[id]/react/route\",\n        pathname: \"/api/comments/[id]/react\",\n        filename: \"route\",\n        bundlePath: \"app/api/comments/[id]/react/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\comments\\\\[id]\\\\react\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_id_react_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/comments/[id]/react/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&page=%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/comments/[id]/react/route.js":
/*!**********************************************!*\
  !*** ./app/api/comments/[id]/react/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n/* harmony import */ var _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CommentModel */ \"(rsc)/./lib/models/CommentModel.js\");\n/* harmony import */ var _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/models/CommentReactionModel */ \"(rsc)/./lib/models/CommentReactionModel.js\");\n/* harmony import */ var _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/models/CommentSettingsModel */ \"(rsc)/./lib/models/CommentSettingsModel.js\");\n\n\n\n\n\n\n// Helper function to get user from token\nfunction getUserFromToken(request) {\n    const authHeader = request.headers.get(\"Authorization\");\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    const token = authHeader.split(\" \")[1];\n    return (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n}\n// Helper function to check if reactions are enabled\nasync function checkReactionsEnabled(blogId = null) {\n    try {\n        // Check blog-specific settings first, then global settings\n        let settings = null;\n        if (blogId) {\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                blogId\n            });\n        }\n        if (!settings) {\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                blogId: null\n            });\n        }\n        return settings ? settings.allowReactions : true; // Default to enabled\n    } catch (error) {\n        console.error(\"Error checking reaction settings:\", error);\n        return true; // Default to enabled on error\n    }\n}\n// POST - Add or update a reaction to a comment\nasync function POST(request, { params }) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const commentId = params.id;\n        const body = await request.json();\n        const { reactionType } = body;\n        if (!reactionType || ![\n            \"like\",\n            \"dislike\"\n        ].includes(reactionType)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Valid reaction type (like/dislike) is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Verify comment exists\n        const comment = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(commentId);\n        if (!comment || comment.isDeleted) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comment not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if reactions are enabled\n        const reactionsEnabled = await checkReactionsEnabled(comment.blogId);\n        if (!reactionsEnabled) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Reactions are disabled for this blog\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if user already has a reaction on this comment\n        const existingReaction = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n            commentId,\n            userId: userData.userId\n        });\n        if (existingReaction) {\n            if (existingReaction.reactionType === reactionType) {\n                // Same reaction - remove it (toggle off)\n                await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].deleteOne({\n                    _id: existingReaction._id\n                });\n                // Get updated counts\n                const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId,\n                    reactionType: \"like\"\n                });\n                const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId,\n                    reactionType: \"dislike\"\n                });\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: true,\n                    message: \"Reaction removed\",\n                    reaction: null,\n                    likeCount,\n                    dislikeCount\n                });\n            } else {\n                // Different reaction - update it\n                existingReaction.reactionType = reactionType;\n                await existingReaction.save();\n                // Get updated counts\n                const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId,\n                    reactionType: \"like\"\n                });\n                const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId,\n                    reactionType: \"dislike\"\n                });\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: true,\n                    message: \"Reaction updated\",\n                    reaction: reactionType,\n                    likeCount,\n                    dislikeCount\n                });\n            }\n        } else {\n            // No existing reaction - create new one\n            await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].create({\n                commentId,\n                userId: userData.userId,\n                reactionType\n            });\n            // Get updated counts\n            const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId,\n                reactionType: \"like\"\n            });\n            const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId,\n                reactionType: \"dislike\"\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Reaction added\",\n                reaction: reactionType,\n                likeCount,\n                dislikeCount\n            });\n        }\n    } catch (error) {\n        console.error(\"Error handling comment reaction:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to process reaction\"\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - Get user's reaction status for a comment\nasync function GET(request, { params }) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const commentId = params.id;\n        // Get user's reaction\n        const userReaction = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n            commentId,\n            userId: userData.userId\n        });\n        // Get total counts\n        const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            commentId,\n            reactionType: \"like\"\n        });\n        const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            commentId,\n            reactionType: \"dislike\"\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            userReaction: userReaction ? userReaction.reactionType : null,\n            likeCount,\n            dislikeCount\n        });\n    } catch (error) {\n        console.error(\"Error fetching comment reaction:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch reaction status\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/comments/[id]/react/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentModel.js":
/*!************************************!*\
  !*** ./lib/models/CommentModel.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    content: {\n        type: String,\n        required: true,\n        trim: true,\n        maxlength: 1000\n    },\n    parentCommentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        default: null,\n        index: true\n    },\n    isDeleted: {\n        type: Boolean,\n        default: false\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Update the updatedAt field before saving\nCommentSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Virtual for getting replies\nCommentSchema.virtual(\"replies\", {\n    ref: \"Comment\",\n    localField: \"_id\",\n    foreignField: \"parentCommentId\"\n});\n// Virtual for getting like count\nCommentSchema.virtual(\"likeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"like\"\n    }\n});\n// Virtual for getting dislike count\nCommentSchema.virtual(\"dislikeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"dislike\"\n    }\n});\n// Ensure virtual fields are serialized\nCommentSchema.set(\"toJSON\", {\n    virtuals: true\n});\nCommentSchema.set(\"toObject\", {\n    virtuals: true\n});\nconst CommentModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Comment || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Comment\", CommentSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0NvbW1lbnRNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsZ0JBQWdCLElBQUlELHdEQUFlLENBQUM7SUFDeENHLFFBQVE7UUFDTkMsTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FDLFFBQVE7UUFDTk4sTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FFLFNBQVM7UUFDUFAsTUFBTVE7UUFDTkosVUFBVTtRQUNWSyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBQyxpQkFBaUI7UUFDZlgsTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xTLFNBQVM7UUFDVFAsT0FBTztJQUNUO0lBQ0FRLFdBQVc7UUFDVGIsTUFBTWM7UUFDTkYsU0FBUztJQUNYO0lBQ0FHLFdBQVc7UUFDVGYsTUFBTWdCO1FBQ05KLFNBQVNJLEtBQUtDLEdBQUc7UUFDakJaLE9BQU87SUFDVDtJQUNBYSxXQUFXO1FBQ1RsQixNQUFNZ0I7UUFDTkosU0FBU0ksS0FBS0MsR0FBRztJQUNuQjtBQUNGO0FBRUEsMkNBQTJDO0FBQzNDcEIsY0FBY3NCLEdBQUcsQ0FBQyxRQUFRLFNBQVNDLElBQUk7SUFDckMsSUFBSSxDQUFDRixTQUFTLEdBQUdGLEtBQUtDLEdBQUc7SUFDekJHO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDOUJ2QixjQUFjd0IsT0FBTyxDQUFDLFdBQVc7SUFDL0JsQixLQUFLO0lBQ0xtQixZQUFZO0lBQ1pDLGNBQWM7QUFDaEI7QUFFQSxpQ0FBaUM7QUFDakMxQixjQUFjd0IsT0FBTyxDQUFDLGFBQWE7SUFDakNsQixLQUFLO0lBQ0xtQixZQUFZO0lBQ1pDLGNBQWM7SUFDZEMsT0FBTztJQUNQQyxPQUFPO1FBQUVDLGNBQWM7SUFBTztBQUNoQztBQUVBLG9DQUFvQztBQUNwQzdCLGNBQWN3QixPQUFPLENBQUMsZ0JBQWdCO0lBQ3BDbEIsS0FBSztJQUNMbUIsWUFBWTtJQUNaQyxjQUFjO0lBQ2RDLE9BQU87SUFDUEMsT0FBTztRQUFFQyxjQUFjO0lBQVU7QUFDbkM7QUFFQSx1Q0FBdUM7QUFDdkM3QixjQUFjOEIsR0FBRyxDQUFDLFVBQVU7SUFBRUMsVUFBVTtBQUFLO0FBQzdDL0IsY0FBYzhCLEdBQUcsQ0FBQyxZQUFZO0lBQUVDLFVBQVU7QUFBSztBQUUvQyxNQUFNQyxlQUFlakMsd0RBQWUsQ0FBQ21DLE9BQU8sSUFBSW5DLHFEQUFjLENBQUMsV0FBV0M7QUFFMUUsaUVBQWVnQyxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQ29tbWVudE1vZGVsLmpzPzMxNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBDb21tZW50U2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIGJsb2dJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwiYmxvZ1wiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIHVzZXJJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwidXNlclwiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIGNvbnRlbnQ6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IDEwMDBcbiAgfSxcbiAgcGFyZW50Q29tbWVudElkOiB7XG4gICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgIHJlZjogXCJDb21tZW50XCIsXG4gICAgZGVmYXVsdDogbnVsbCxcbiAgICBpbmRleDogdHJ1ZVxuICB9LFxuICBpc0RlbGV0ZWQ6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IGZhbHNlXG4gIH0sXG4gIGNyZWF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3csXG4gICAgaW5kZXg6IHRydWVcbiAgfSxcbiAgdXBkYXRlZEF0OiB7XG4gICAgdHlwZTogRGF0ZSxcbiAgICBkZWZhdWx0OiBEYXRlLm5vd1xuICB9XG59KTtcblxuLy8gVXBkYXRlIHRoZSB1cGRhdGVkQXQgZmllbGQgYmVmb3JlIHNhdmluZ1xuQ29tbWVudFNjaGVtYS5wcmUoJ3NhdmUnLCBmdW5jdGlvbihuZXh0KSB7XG4gIHRoaXMudXBkYXRlZEF0ID0gRGF0ZS5ub3coKTtcbiAgbmV4dCgpO1xufSk7XG5cbi8vIFZpcnR1YWwgZm9yIGdldHRpbmcgcmVwbGllc1xuQ29tbWVudFNjaGVtYS52aXJ0dWFsKCdyZXBsaWVzJywge1xuICByZWY6ICdDb21tZW50JyxcbiAgbG9jYWxGaWVsZDogJ19pZCcsXG4gIGZvcmVpZ25GaWVsZDogJ3BhcmVudENvbW1lbnRJZCdcbn0pO1xuXG4vLyBWaXJ0dWFsIGZvciBnZXR0aW5nIGxpa2UgY291bnRcbkNvbW1lbnRTY2hlbWEudmlydHVhbCgnbGlrZUNvdW50Jywge1xuICByZWY6ICdDb21tZW50UmVhY3Rpb24nLFxuICBsb2NhbEZpZWxkOiAnX2lkJyxcbiAgZm9yZWlnbkZpZWxkOiAnY29tbWVudElkJyxcbiAgY291bnQ6IHRydWUsXG4gIG1hdGNoOiB7IHJlYWN0aW9uVHlwZTogJ2xpa2UnIH1cbn0pO1xuXG4vLyBWaXJ0dWFsIGZvciBnZXR0aW5nIGRpc2xpa2UgY291bnRcbkNvbW1lbnRTY2hlbWEudmlydHVhbCgnZGlzbGlrZUNvdW50Jywge1xuICByZWY6ICdDb21tZW50UmVhY3Rpb24nLFxuICBsb2NhbEZpZWxkOiAnX2lkJyxcbiAgZm9yZWlnbkZpZWxkOiAnY29tbWVudElkJyxcbiAgY291bnQ6IHRydWUsXG4gIG1hdGNoOiB7IHJlYWN0aW9uVHlwZTogJ2Rpc2xpa2UnIH1cbn0pO1xuXG4vLyBFbnN1cmUgdmlydHVhbCBmaWVsZHMgYXJlIHNlcmlhbGl6ZWRcbkNvbW1lbnRTY2hlbWEuc2V0KCd0b0pTT04nLCB7IHZpcnR1YWxzOiB0cnVlIH0pO1xuQ29tbWVudFNjaGVtYS5zZXQoJ3RvT2JqZWN0JywgeyB2aXJ0dWFsczogdHJ1ZSB9KTtcblxuY29uc3QgQ29tbWVudE1vZGVsID0gbW9uZ29vc2UubW9kZWxzLkNvbW1lbnQgfHwgbW9uZ29vc2UubW9kZWwoJ0NvbW1lbnQnLCBDb21tZW50U2NoZW1hKTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tbWVudE1vZGVsO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiQ29tbWVudFNjaGVtYSIsIlNjaGVtYSIsImJsb2dJZCIsInR5cGUiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwicmVxdWlyZWQiLCJpbmRleCIsInVzZXJJZCIsImNvbnRlbnQiLCJTdHJpbmciLCJ0cmltIiwibWF4bGVuZ3RoIiwicGFyZW50Q29tbWVudElkIiwiZGVmYXVsdCIsImlzRGVsZXRlZCIsIkJvb2xlYW4iLCJjcmVhdGVkQXQiLCJEYXRlIiwibm93IiwidXBkYXRlZEF0IiwicHJlIiwibmV4dCIsInZpcnR1YWwiLCJsb2NhbEZpZWxkIiwiZm9yZWlnbkZpZWxkIiwiY291bnQiLCJtYXRjaCIsInJlYWN0aW9uVHlwZSIsInNldCIsInZpcnR1YWxzIiwiQ29tbWVudE1vZGVsIiwibW9kZWxzIiwiQ29tbWVudCIsIm1vZGVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentReactionModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentReactionModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentReactionSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    commentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    reactionType: {\n        type: String,\n        required: true,\n        enum: [\n            \"like\",\n            \"dislike\"\n        ],\n        index: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Create compound index to ensure a user can only have one reaction per comment\nCommentReactionSchema.index({\n    commentId: 1,\n    userId: 1\n}, {\n    unique: true\n});\nconst CommentReactionModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentReaction || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentReaction\", CommentReactionSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentReactionModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0NvbW1lbnRSZWFjdGlvbk1vZGVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUVoQyxNQUFNQyx3QkFBd0IsSUFBSUQsd0RBQWUsQ0FBQztJQUNoREcsV0FBVztRQUNUQyxNQUFNSix3REFBZSxDQUFDSyxLQUFLLENBQUNDLFFBQVE7UUFDcENDLEtBQUs7UUFDTEMsVUFBVTtRQUNWQyxPQUFPO0lBQ1Q7SUFDQUMsUUFBUTtRQUNOTixNQUFNSix3REFBZSxDQUFDSyxLQUFLLENBQUNDLFFBQVE7UUFDcENDLEtBQUs7UUFDTEMsVUFBVTtRQUNWQyxPQUFPO0lBQ1Q7SUFDQUUsY0FBYztRQUNaUCxNQUFNUTtRQUNOSixVQUFVO1FBQ1ZLLE1BQU07WUFBQztZQUFRO1NBQVU7UUFDekJKLE9BQU87SUFDVDtJQUNBSyxXQUFXO1FBQ1RWLE1BQU1XO1FBQ05DLFNBQVNELEtBQUtFLEdBQUc7SUFDbkI7QUFDRjtBQUVBLGdGQUFnRjtBQUNoRmhCLHNCQUFzQlEsS0FBSyxDQUFDO0lBQUVOLFdBQVc7SUFBR08sUUFBUTtBQUFFLEdBQUc7SUFBRVEsUUFBUTtBQUFLO0FBRXhFLE1BQU1DLHVCQUF1Qm5CLHdEQUFlLENBQUNxQixlQUFlLElBQUlyQixxREFBYyxDQUFDLG1CQUFtQkM7QUFFbEcsaUVBQWVrQixvQkFBb0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vbGliL21vZGVscy9Db21tZW50UmVhY3Rpb25Nb2RlbC5qcz81MjQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb25nb29zZSBmcm9tIFwibW9uZ29vc2VcIjtcblxuY29uc3QgQ29tbWVudFJlYWN0aW9uU2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIGNvbW1lbnRJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwiQ29tbWVudFwiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIHVzZXJJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwidXNlclwiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIHJlYWN0aW9uVHlwZToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICBlbnVtOiBbJ2xpa2UnLCAnZGlzbGlrZSddLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIGNyZWF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3dcbiAgfVxufSk7XG5cbi8vIENyZWF0ZSBjb21wb3VuZCBpbmRleCB0byBlbnN1cmUgYSB1c2VyIGNhbiBvbmx5IGhhdmUgb25lIHJlYWN0aW9uIHBlciBjb21tZW50XG5Db21tZW50UmVhY3Rpb25TY2hlbWEuaW5kZXgoeyBjb21tZW50SWQ6IDEsIHVzZXJJZDogMSB9LCB7IHVuaXF1ZTogdHJ1ZSB9KTtcblxuY29uc3QgQ29tbWVudFJlYWN0aW9uTW9kZWwgPSBtb25nb29zZS5tb2RlbHMuQ29tbWVudFJlYWN0aW9uIHx8IG1vbmdvb3NlLm1vZGVsKCdDb21tZW50UmVhY3Rpb24nLCBDb21tZW50UmVhY3Rpb25TY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBDb21tZW50UmVhY3Rpb25Nb2RlbDtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIkNvbW1lbnRSZWFjdGlvblNjaGVtYSIsIlNjaGVtYSIsImNvbW1lbnRJZCIsInR5cGUiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwicmVxdWlyZWQiLCJpbmRleCIsInVzZXJJZCIsInJlYWN0aW9uVHlwZSIsIlN0cmluZyIsImVudW0iLCJjcmVhdGVkQXQiLCJEYXRlIiwiZGVmYXVsdCIsIm5vdyIsInVuaXF1ZSIsIkNvbW1lbnRSZWFjdGlvbk1vZGVsIiwibW9kZWxzIiwiQ29tbWVudFJlYWN0aW9uIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentReactionModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentSettingsModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentSettingsModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSettingsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Global settings\n    commentsEnabled: {\n        type: Boolean,\n        default: true\n    },\n    requireLogin: {\n        type: Boolean,\n        default: true\n    },\n    allowReplies: {\n        type: Boolean,\n        default: true\n    },\n    allowReactions: {\n        type: Boolean,\n        default: true\n    },\n    maxCommentLength: {\n        type: Number,\n        default: 1000,\n        min: 100,\n        max: 5000\n    },\n    moderationEnabled: {\n        type: Boolean,\n        default: false\n    },\n    // Per-blog settings (optional)\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        default: null,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true\n    }\n});\n// Update the updatedAt field before saving\nCommentSettingsSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Ensure only one global setting (where blogId is null)\nCommentSettingsSchema.index({\n    blogId: 1\n}, {\n    unique: true,\n    sparse: true\n});\nconst CommentSettingsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentSettings || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentSettings\", CommentSettingsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentSettingsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentSettingsModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&page=%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2F%5Bid%5D%2Freact%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();