import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import { verifyToken } from '@/lib/utils/token';
import CommentModel from '@/lib/models/CommentModel';
import CommentReactionModel from '@/lib/models/CommentReactionModel';
import BlogModel from '@/lib/models/BlogModel';

// Helper function to get user from token
function getUserFromToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.split(' ')[1];
  return verifyToken(token);
}

// Helper function to check if user is admin
function isAdmin(userData) {
  return userData && userData.role === 'admin';
}

// GET - Fetch all comments for admin management
export async function GET(request) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData || !isAdmin(userData)) {
      return NextResponse.json({ 
        success: false, 
        message: "Admin access required" 
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const blogId = searchParams.get('blogId');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build query
    const query = { isDeleted: false };
    if (blogId) {
      query.blogId = blogId;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Fetch comments with user and blog info
    const comments = await CommentModel.find(query)
      .populate('userId', 'name email profilePicture')
      .populate('blogId', 'title')
      .populate('parentCommentId', 'content userId')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Add reaction counts to each comment
    const commentsWithDetails = await Promise.all(
      comments.map(async (comment) => {
        const likeCount = await CommentReactionModel.countDocuments({ 
          commentId: comment._id, 
          reactionType: 'like' 
        });
        const dislikeCount = await CommentReactionModel.countDocuments({ 
          commentId: comment._id, 
          reactionType: 'dislike' 
        });

        // Count replies if this is a top-level comment
        const replyCount = comment.parentCommentId ? 0 : await CommentModel.countDocuments({
          parentCommentId: comment._id,
          isDeleted: false
        });

        return {
          ...comment.toObject(),
          likeCount,
          dislikeCount,
          replyCount
        };
      })
    );

    // Get total count for pagination
    const totalComments = await CommentModel.countDocuments(query);

    // Get summary statistics
    const totalAllComments = await CommentModel.countDocuments({ isDeleted: false });
    const totalDeletedComments = await CommentModel.countDocuments({ isDeleted: true });
    const totalReactions = await CommentReactionModel.countDocuments();

    return NextResponse.json({ 
      success: true, 
      comments: commentsWithDetails,
      pagination: {
        page,
        limit,
        total: totalComments,
        pages: Math.ceil(totalComments / limit)
      },
      statistics: {
        totalComments: totalAllComments,
        totalDeletedComments,
        totalReactions
      }
    });
  } catch (error) {
    console.error("Error fetching admin comments:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch comments" 
    }, { status: 500 });
  }
}

// DELETE - Delete a comment (Admin only)
export async function DELETE(request) {
  try {
    await ConnectDB();
    
    const userData = getUserFromToken(request);
    if (!userData || !isAdmin(userData)) {
      return NextResponse.json({ 
        success: false, 
        message: "Admin access required" 
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return NextResponse.json({ 
        success: false, 
        message: "Comment ID is required" 
      }, { status: 400 });
    }

    // Find the comment
    const comment = await CommentModel.findById(commentId);
    if (!comment) {
      return NextResponse.json({ 
        success: false, 
        message: "Comment not found" 
      }, { status: 404 });
    }

    // Mark comment as deleted (soft delete)
    comment.isDeleted = true;
    await comment.save();

    // Also mark all replies as deleted
    await CommentModel.updateMany(
      { parentCommentId: commentId },
      { isDeleted: true }
    );

    return NextResponse.json({ 
      success: true, 
      message: "Comment and its replies deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting comment:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to delete comment" 
    }, { status: 500 });
  }
}
