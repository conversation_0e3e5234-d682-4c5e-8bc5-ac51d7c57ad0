"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-easy-crop";
exports.ids = ["vendor-chunks/react-easy-crop"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-easy-crop/index.module.js":
/*!******************************************************!*\
  !*** ./node_modules/react-easy-crop/index.module.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cropper),\n/* harmony export */   getInitialCropFromCroppedAreaPercentages: () => (/* binding */ getInitialCropFromCroppedAreaPercentages),\n/* harmony export */   getInitialCropFromCroppedAreaPixels: () => (/* binding */ getInitialCropFromCroppedAreaPixels)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var normalize_wheel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! normalize-wheel */ \"(ssr)/./node_modules/normalize-wheel/index.js\");\n/* harmony import */ var normalize_wheel__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(normalize_wheel__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n/**\r\n * Compute the dimension of the crop area based on media size,\r\n * aspect ratio and optionally rotation\r\n */ function getCropSize(mediaWidth, mediaHeight, containerWidth, containerHeight, aspect, rotation) {\n    if (rotation === void 0) {\n        rotation = 0;\n    }\n    var _a = rotateSize(mediaWidth, mediaHeight, rotation), width = _a.width, height = _a.height;\n    var fittingWidth = Math.min(width, containerWidth);\n    var fittingHeight = Math.min(height, containerHeight);\n    if (fittingWidth > fittingHeight * aspect) {\n        return {\n            width: fittingHeight * aspect,\n            height: fittingHeight\n        };\n    }\n    return {\n        width: fittingWidth,\n        height: fittingWidth / aspect\n    };\n}\n/**\r\n * Compute media zoom.\r\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\r\n */ function getMediaZoom(mediaSize) {\n    // Take the axis with more pixels to improve accuracy\n    return mediaSize.width > mediaSize.height ? mediaSize.width / mediaSize.naturalWidth : mediaSize.height / mediaSize.naturalHeight;\n}\n/**\r\n * Ensure a new media position stays in the crop area.\r\n */ function restrictPosition(position, mediaSize, cropSize, zoom, rotation) {\n    if (rotation === void 0) {\n        rotation = 0;\n    }\n    var _a = rotateSize(mediaSize.width, mediaSize.height, rotation), width = _a.width, height = _a.height;\n    return {\n        x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n        y: restrictPositionCoord(position.y, height, cropSize.height, zoom)\n    };\n}\nfunction restrictPositionCoord(position, mediaSize, cropSize, zoom) {\n    var maxPosition = mediaSize * zoom / 2 - cropSize / 2;\n    return clamp(position, -maxPosition, maxPosition);\n}\nfunction getDistanceBetweenPoints(pointA, pointB) {\n    return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2));\n}\nfunction getRotationBetweenPoints(pointA, pointB) {\n    return Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180 / Math.PI;\n}\n/**\r\n * Compute the output cropped area of the media in percentages and pixels.\r\n * x/y are the top-left coordinates on the src media\r\n */ function computeCroppedArea(crop, mediaSize, cropSize, aspect, zoom, rotation, restrictPosition) {\n    if (rotation === void 0) {\n        rotation = 0;\n    }\n    if (restrictPosition === void 0) {\n        restrictPosition = true;\n    }\n    // if the media is rotated by the user, we cannot limit the position anymore\n    // as it might need to be negative.\n    var limitAreaFn = restrictPosition ? limitArea : noOp;\n    var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n    var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n    // calculate the crop area in percentages\n    // in the rotated space\n    var croppedAreaPercentages = {\n        x: limitAreaFn(100, ((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width * 100),\n        y: limitAreaFn(100, ((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) / mediaBBoxSize.height * 100),\n        width: limitAreaFn(100, cropSize.width / mediaBBoxSize.width * 100 / zoom),\n        height: limitAreaFn(100, cropSize.height / mediaBBoxSize.height * 100 / zoom)\n    };\n    // we compute the pixels size naively\n    var widthInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.width, croppedAreaPercentages.width * mediaNaturalBBoxSize.width / 100));\n    var heightInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.height, croppedAreaPercentages.height * mediaNaturalBBoxSize.height / 100));\n    var isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect;\n    // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n    // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n    // thus we want to compute the width from the height and aspect for accuracy.\n    // Otherwise, we compute the height from width and aspect.\n    var sizePixels = isImgWiderThanHigh ? {\n        width: Math.round(heightInPixels * aspect),\n        height: heightInPixels\n    } : {\n        width: widthInPixels,\n        height: Math.round(widthInPixels / aspect)\n    };\n    var croppedAreaPixels = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, sizePixels), {\n        x: Math.round(limitAreaFn(mediaNaturalBBoxSize.width - sizePixels.width, croppedAreaPercentages.x * mediaNaturalBBoxSize.width / 100)),\n        y: Math.round(limitAreaFn(mediaNaturalBBoxSize.height - sizePixels.height, croppedAreaPercentages.y * mediaNaturalBBoxSize.height / 100))\n    });\n    return {\n        croppedAreaPercentages: croppedAreaPercentages,\n        croppedAreaPixels: croppedAreaPixels\n    };\n}\n/**\r\n * Ensure the returned value is between 0 and max\r\n */ function limitArea(max, value) {\n    return Math.min(max, Math.max(0, value));\n}\nfunction noOp(_max, value) {\n    return value;\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPercentages.\r\n */ function getInitialCropFromCroppedAreaPercentages(croppedAreaPercentages, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n    var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n    // This is the inverse process of computeCroppedArea\n    var zoom = clamp(cropSize.width / mediaBBoxSize.width * (100 / croppedAreaPercentages.width), minZoom, maxZoom);\n    var crop = {\n        x: zoom * mediaBBoxSize.width / 2 - cropSize.width / 2 - mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n        y: zoom * mediaBBoxSize.height / 2 - cropSize.height / 2 - mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100)\n    };\n    return {\n        crop: crop,\n        zoom: zoom\n    };\n}\n/**\r\n * Compute zoom from the croppedAreaPixels\r\n */ function getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize) {\n    var mediaZoom = getMediaZoom(mediaSize);\n    return cropSize.height > cropSize.width ? cropSize.height / (croppedAreaPixels.height * mediaZoom) : cropSize.width / (croppedAreaPixels.width * mediaZoom);\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPixels\r\n */ function getInitialCropFromCroppedAreaPixels(croppedAreaPixels, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n    if (rotation === void 0) {\n        rotation = 0;\n    }\n    var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n    var zoom = clamp(getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize), minZoom, maxZoom);\n    var cropZoom = cropSize.height > cropSize.width ? cropSize.height / croppedAreaPixels.height : cropSize.width / croppedAreaPixels.width;\n    var crop = {\n        x: ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n        y: ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) * cropZoom\n    };\n    return {\n        crop: crop,\n        zoom: zoom\n    };\n}\n/**\r\n * Return the point that is the center of point a and b\r\n */ function getCenter(a, b) {\n    return {\n        x: (b.x + a.x) / 2,\n        y: (b.y + a.y) / 2\n    };\n}\nfunction getRadianAngle(degreeValue) {\n    return degreeValue * Math.PI / 180;\n}\n/**\r\n * Returns the new bounding area of a rotated rectangle.\r\n */ function rotateSize(width, height, rotation) {\n    var rotRad = getRadianAngle(rotation);\n    return {\n        width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n        height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height)\n    };\n}\n/**\r\n * Clamp value between min and max\r\n */ function clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n/**\r\n * Combine multiple class names into a single string.\r\n */ function classNames() {\n    var args = [];\n    for(var _i = 0; _i < arguments.length; _i++){\n        args[_i] = arguments[_i];\n    }\n    return args.filter(function(value) {\n        if (typeof value === \"string\" && value.length > 0) {\n            return true;\n        }\n        return false;\n    }).join(\" \").trim();\n}\nvar css_248z = \".reactEasyCrop_Container {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  overflow: hidden;\\n  user-select: none;\\n  touch-action: none;\\n  cursor: move;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.reactEasyCrop_Image,\\n.reactEasyCrop_Video {\\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\\n}\\n\\n.reactEasyCrop_Contain {\\n  max-width: 100%;\\n  max-height: 100%;\\n  margin: auto;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n.reactEasyCrop_Cover_Horizontal {\\n  width: 100%;\\n  height: auto;\\n}\\n.reactEasyCrop_Cover_Vertical {\\n  width: auto;\\n  height: 100%;\\n}\\n\\n.reactEasyCrop_CropArea {\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  box-sizing: border-box;\\n  box-shadow: 0 0 0 9999em;\\n  color: rgba(0, 0, 0, 0.5);\\n  overflow: hidden;\\n}\\n\\n.reactEasyCrop_CropAreaRound {\\n  border-radius: 50%;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::before {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 0;\\n  bottom: 0;\\n  left: 33.33%;\\n  right: 33.33%;\\n  border-top: 0;\\n  border-bottom: 0;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::after {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 33.33%;\\n  bottom: 33.33%;\\n  left: 0;\\n  right: 0;\\n  border-left: 0;\\n  border-right: 0;\\n}\\n\";\nvar MIN_ZOOM = 1;\nvar MAX_ZOOM = 3;\nvar KEYBOARD_STEP = 1;\nvar Cropper = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__extends)(Cropper, _super);\n    function Cropper() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.cropperRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n        _this.imageRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n        _this.videoRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n        _this.containerPosition = {\n            x: 0,\n            y: 0\n        };\n        _this.containerRef = null;\n        _this.styleRef = null;\n        _this.containerRect = null;\n        _this.mediaSize = {\n            width: 0,\n            height: 0,\n            naturalWidth: 0,\n            naturalHeight: 0\n        };\n        _this.dragStartPosition = {\n            x: 0,\n            y: 0\n        };\n        _this.dragStartCrop = {\n            x: 0,\n            y: 0\n        };\n        _this.gestureZoomStart = 0;\n        _this.gestureRotationStart = 0;\n        _this.isTouching = false;\n        _this.lastPinchDistance = 0;\n        _this.lastPinchRotation = 0;\n        _this.rafDragTimeout = null;\n        _this.rafPinchTimeout = null;\n        _this.wheelTimer = null;\n        _this.currentDoc = typeof document !== \"undefined\" ? document : null;\n        _this.currentWindow =  false ? 0 : null;\n        _this.resizeObserver = null;\n        _this.state = {\n            cropSize: null,\n            hasWheelJustStarted: false,\n            mediaObjectFit: undefined\n        };\n        _this.initResizeObserver = function() {\n            if (typeof window.ResizeObserver === \"undefined\" || !_this.containerRef) {\n                return;\n            }\n            var isFirstResize = true;\n            _this.resizeObserver = new window.ResizeObserver(function(entries) {\n                if (isFirstResize) {\n                    isFirstResize = false; // observe() is called on mount, we don't want to trigger a recompute on mount\n                    return;\n                }\n                _this.computeSizes();\n            });\n            _this.resizeObserver.observe(_this.containerRef);\n        };\n        // this is to prevent Safari on iOS >= 10 to zoom the page\n        _this.preventZoomSafari = function(e) {\n            return e.preventDefault();\n        };\n        _this.cleanEvents = function() {\n            if (!_this.currentDoc) return;\n            _this.currentDoc.removeEventListener(\"mousemove\", _this.onMouseMove);\n            _this.currentDoc.removeEventListener(\"mouseup\", _this.onDragStopped);\n            _this.currentDoc.removeEventListener(\"touchmove\", _this.onTouchMove);\n            _this.currentDoc.removeEventListener(\"touchend\", _this.onDragStopped);\n            _this.currentDoc.removeEventListener(\"gesturechange\", _this.onGestureChange);\n            _this.currentDoc.removeEventListener(\"gestureend\", _this.onGestureEnd);\n            _this.currentDoc.removeEventListener(\"scroll\", _this.onScroll);\n        };\n        _this.clearScrollEvent = function() {\n            if (_this.containerRef) _this.containerRef.removeEventListener(\"wheel\", _this.onWheel);\n            if (_this.wheelTimer) {\n                clearTimeout(_this.wheelTimer);\n            }\n        };\n        _this.onMediaLoad = function() {\n            var cropSize = _this.computeSizes();\n            if (cropSize) {\n                _this.emitCropData();\n                _this.setInitialCrop(cropSize);\n            }\n            if (_this.props.onMediaLoaded) {\n                _this.props.onMediaLoaded(_this.mediaSize);\n            }\n        };\n        _this.setInitialCrop = function(cropSize) {\n            if (_this.props.initialCroppedAreaPercentages) {\n                var _a = getInitialCropFromCroppedAreaPercentages(_this.props.initialCroppedAreaPercentages, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom), crop = _a.crop, zoom = _a.zoom;\n                _this.props.onCropChange(crop);\n                _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n            } else if (_this.props.initialCroppedAreaPixels) {\n                var _b = getInitialCropFromCroppedAreaPixels(_this.props.initialCroppedAreaPixels, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom), crop = _b.crop, zoom = _b.zoom;\n                _this.props.onCropChange(crop);\n                _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n            }\n        };\n        _this.computeSizes = function() {\n            var _a, _b, _c, _d, _e, _f;\n            var mediaRef = _this.imageRef.current || _this.videoRef.current;\n            if (mediaRef && _this.containerRef) {\n                _this.containerRect = _this.containerRef.getBoundingClientRect();\n                _this.saveContainerPosition();\n                var containerAspect = _this.containerRect.width / _this.containerRect.height;\n                var naturalWidth = ((_a = _this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = _this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n                var naturalHeight = ((_c = _this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = _this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n                var isMediaScaledDown = mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight;\n                var mediaAspect = naturalWidth / naturalHeight;\n                // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n                // as the values they report are rounded. That will result in precision losses\n                // when calculating zoom. We use the fact that the media is positionned relative\n                // to the container. That allows us to use the container's dimensions\n                // and natural aspect ratio of the media to calculate accurate media size.\n                // However, for this to work, the container should not be rotated\n                var renderedMediaSize = void 0;\n                if (isMediaScaledDown) {\n                    switch(_this.state.mediaObjectFit){\n                        default:\n                        case \"contain\":\n                            renderedMediaSize = containerAspect > mediaAspect ? {\n                                width: _this.containerRect.height * mediaAspect,\n                                height: _this.containerRect.height\n                            } : {\n                                width: _this.containerRect.width,\n                                height: _this.containerRect.width / mediaAspect\n                            };\n                            break;\n                        case \"horizontal-cover\":\n                            renderedMediaSize = {\n                                width: _this.containerRect.width,\n                                height: _this.containerRect.width / mediaAspect\n                            };\n                            break;\n                        case \"vertical-cover\":\n                            renderedMediaSize = {\n                                width: _this.containerRect.height * mediaAspect,\n                                height: _this.containerRect.height\n                            };\n                            break;\n                    }\n                } else {\n                    renderedMediaSize = {\n                        width: mediaRef.offsetWidth,\n                        height: mediaRef.offsetHeight\n                    };\n                }\n                _this.mediaSize = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, renderedMediaSize), {\n                    naturalWidth: naturalWidth,\n                    naturalHeight: naturalHeight\n                });\n                // set media size in the parent\n                if (_this.props.setMediaSize) {\n                    _this.props.setMediaSize(_this.mediaSize);\n                }\n                var cropSize = _this.props.cropSize ? _this.props.cropSize : getCropSize(_this.mediaSize.width, _this.mediaSize.height, _this.containerRect.width, _this.containerRect.height, _this.props.aspect, _this.props.rotation);\n                if (((_e = _this.state.cropSize) === null || _e === void 0 ? void 0 : _e.height) !== cropSize.height || ((_f = _this.state.cropSize) === null || _f === void 0 ? void 0 : _f.width) !== cropSize.width) {\n                    _this.props.onCropSizeChange && _this.props.onCropSizeChange(cropSize);\n                }\n                _this.setState({\n                    cropSize: cropSize\n                }, _this.recomputeCropPosition);\n                // pass crop size to parent\n                if (_this.props.setCropSize) {\n                    _this.props.setCropSize(cropSize);\n                }\n                return cropSize;\n            }\n        };\n        _this.saveContainerPosition = function() {\n            if (_this.containerRef) {\n                var bounds = _this.containerRef.getBoundingClientRect();\n                _this.containerPosition = {\n                    x: bounds.left,\n                    y: bounds.top\n                };\n            }\n        };\n        _this.onMouseDown = function(e) {\n            if (!_this.currentDoc) return;\n            e.preventDefault();\n            _this.currentDoc.addEventListener(\"mousemove\", _this.onMouseMove);\n            _this.currentDoc.addEventListener(\"mouseup\", _this.onDragStopped);\n            _this.saveContainerPosition();\n            _this.onDragStart(Cropper.getMousePoint(e));\n        };\n        _this.onMouseMove = function(e) {\n            return _this.onDrag(Cropper.getMousePoint(e));\n        };\n        _this.onScroll = function(e) {\n            if (!_this.currentDoc) return;\n            e.preventDefault();\n            _this.saveContainerPosition();\n        };\n        _this.onTouchStart = function(e) {\n            if (!_this.currentDoc) return;\n            _this.isTouching = true;\n            if (_this.props.onTouchRequest && !_this.props.onTouchRequest(e)) {\n                return;\n            }\n            _this.currentDoc.addEventListener(\"touchmove\", _this.onTouchMove, {\n                passive: false\n            }); // iOS 11 now defaults to passive: true\n            _this.currentDoc.addEventListener(\"touchend\", _this.onDragStopped);\n            _this.saveContainerPosition();\n            if (e.touches.length === 2) {\n                _this.onPinchStart(e);\n            } else if (e.touches.length === 1) {\n                _this.onDragStart(Cropper.getTouchPoint(e.touches[0]));\n            }\n        };\n        _this.onTouchMove = function(e) {\n            // Prevent whole page from scrolling on iOS.\n            e.preventDefault();\n            if (e.touches.length === 2) {\n                _this.onPinchMove(e);\n            } else if (e.touches.length === 1) {\n                _this.onDrag(Cropper.getTouchPoint(e.touches[0]));\n            }\n        };\n        _this.onGestureStart = function(e) {\n            if (!_this.currentDoc) return;\n            e.preventDefault();\n            _this.currentDoc.addEventListener(\"gesturechange\", _this.onGestureChange);\n            _this.currentDoc.addEventListener(\"gestureend\", _this.onGestureEnd);\n            _this.gestureZoomStart = _this.props.zoom;\n            _this.gestureRotationStart = _this.props.rotation;\n        };\n        _this.onGestureChange = function(e) {\n            e.preventDefault();\n            if (_this.isTouching) {\n                // this is to avoid conflict between gesture and touch events\n                return;\n            }\n            var point = Cropper.getMousePoint(e);\n            var newZoom = _this.gestureZoomStart - 1 + e.scale;\n            _this.setNewZoom(newZoom, point, {\n                shouldUpdatePosition: true\n            });\n            if (_this.props.onRotationChange) {\n                var newRotation = _this.gestureRotationStart + e.rotation;\n                _this.props.onRotationChange(newRotation);\n            }\n        };\n        _this.onGestureEnd = function(e) {\n            _this.cleanEvents();\n        };\n        _this.onDragStart = function(_a) {\n            var _b, _c;\n            var x = _a.x, y = _a.y;\n            _this.dragStartPosition = {\n                x: x,\n                y: y\n            };\n            _this.dragStartCrop = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, _this.props.crop);\n            (_c = (_b = _this.props).onInteractionStart) === null || _c === void 0 ? void 0 : _c.call(_b);\n        };\n        _this.onDrag = function(_a) {\n            var x = _a.x, y = _a.y;\n            if (!_this.currentWindow) return;\n            if (_this.rafDragTimeout) _this.currentWindow.cancelAnimationFrame(_this.rafDragTimeout);\n            _this.rafDragTimeout = _this.currentWindow.requestAnimationFrame(function() {\n                if (!_this.state.cropSize) return;\n                if (x === undefined || y === undefined) return;\n                var offsetX = x - _this.dragStartPosition.x;\n                var offsetY = y - _this.dragStartPosition.y;\n                var requestedPosition = {\n                    x: _this.dragStartCrop.x + offsetX,\n                    y: _this.dragStartCrop.y + offsetY\n                };\n                var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : requestedPosition;\n                _this.props.onCropChange(newPosition);\n            });\n        };\n        _this.onDragStopped = function() {\n            var _a, _b;\n            _this.isTouching = false;\n            _this.cleanEvents();\n            _this.emitCropData();\n            (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n        };\n        _this.onWheel = function(e) {\n            if (!_this.currentWindow) return;\n            if (_this.props.onWheelRequest && !_this.props.onWheelRequest(e)) {\n                return;\n            }\n            e.preventDefault();\n            var point = Cropper.getMousePoint(e);\n            var pixelY = normalize_wheel__WEBPACK_IMPORTED_MODULE_1___default()(e).pixelY;\n            var newZoom = _this.props.zoom - pixelY * _this.props.zoomSpeed / 200;\n            _this.setNewZoom(newZoom, point, {\n                shouldUpdatePosition: true\n            });\n            if (!_this.state.hasWheelJustStarted) {\n                _this.setState({\n                    hasWheelJustStarted: true\n                }, function() {\n                    var _a, _b;\n                    return (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n                });\n            }\n            if (_this.wheelTimer) {\n                clearTimeout(_this.wheelTimer);\n            }\n            _this.wheelTimer = _this.currentWindow.setTimeout(function() {\n                return _this.setState({\n                    hasWheelJustStarted: false\n                }, function() {\n                    var _a, _b;\n                    return (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n                });\n            }, 250);\n        };\n        _this.getPointOnContainer = function(_a, containerTopLeft) {\n            var x = _a.x, y = _a.y;\n            if (!_this.containerRect) {\n                throw new Error(\"The Cropper is not mounted\");\n            }\n            return {\n                x: _this.containerRect.width / 2 - (x - containerTopLeft.x),\n                y: _this.containerRect.height / 2 - (y - containerTopLeft.y)\n            };\n        };\n        _this.getPointOnMedia = function(_a) {\n            var x = _a.x, y = _a.y;\n            var _b = _this.props, crop = _b.crop, zoom = _b.zoom;\n            return {\n                x: (x + crop.x) / zoom,\n                y: (y + crop.y) / zoom\n            };\n        };\n        _this.setNewZoom = function(zoom, point, _a) {\n            var _b = _a === void 0 ? {} : _a, _c = _b.shouldUpdatePosition, shouldUpdatePosition = _c === void 0 ? true : _c;\n            if (!_this.state.cropSize || !_this.props.onZoomChange) return;\n            var newZoom = clamp(zoom, _this.props.minZoom, _this.props.maxZoom);\n            if (shouldUpdatePosition) {\n                var zoomPoint = _this.getPointOnContainer(point, _this.containerPosition);\n                var zoomTarget = _this.getPointOnMedia(zoomPoint);\n                var requestedPosition = {\n                    x: zoomTarget.x * newZoom - zoomPoint.x,\n                    y: zoomTarget.y * newZoom - zoomPoint.y\n                };\n                var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, newZoom, _this.props.rotation) : requestedPosition;\n                _this.props.onCropChange(newPosition);\n            }\n            _this.props.onZoomChange(newZoom);\n        };\n        _this.getCropData = function() {\n            if (!_this.state.cropSize) {\n                return null;\n            }\n            // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n            var restrictedPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n            return computeCroppedArea(restrictedPosition, _this.mediaSize, _this.state.cropSize, _this.getAspect(), _this.props.zoom, _this.props.rotation, _this.props.restrictPosition);\n        };\n        _this.emitCropData = function() {\n            var cropData = _this.getCropData();\n            if (!cropData) return;\n            var croppedAreaPercentages = cropData.croppedAreaPercentages, croppedAreaPixels = cropData.croppedAreaPixels;\n            if (_this.props.onCropComplete) {\n                _this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels);\n            }\n            if (_this.props.onCropAreaChange) {\n                _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n            }\n        };\n        _this.emitCropAreaChange = function() {\n            var cropData = _this.getCropData();\n            if (!cropData) return;\n            var croppedAreaPercentages = cropData.croppedAreaPercentages, croppedAreaPixels = cropData.croppedAreaPixels;\n            if (_this.props.onCropAreaChange) {\n                _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n            }\n        };\n        _this.recomputeCropPosition = function() {\n            if (!_this.state.cropSize) return;\n            var newPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n            _this.props.onCropChange(newPosition);\n            _this.emitCropData();\n        };\n        _this.onKeyDown = function(event) {\n            var _a, _b;\n            var _c = _this.props, crop = _c.crop, onCropChange = _c.onCropChange, keyboardStep = _c.keyboardStep, zoom = _c.zoom, rotation = _c.rotation;\n            var step = keyboardStep;\n            if (!_this.state.cropSize) return;\n            // if the shift key is pressed, reduce the step to allow finer control\n            if (event.shiftKey) {\n                step *= 0.2;\n            }\n            var newCrop = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, crop);\n            switch(event.key){\n                case \"ArrowUp\":\n                    newCrop.y -= step;\n                    event.preventDefault();\n                    break;\n                case \"ArrowDown\":\n                    newCrop.y += step;\n                    event.preventDefault();\n                    break;\n                case \"ArrowLeft\":\n                    newCrop.x -= step;\n                    event.preventDefault();\n                    break;\n                case \"ArrowRight\":\n                    newCrop.x += step;\n                    event.preventDefault();\n                    break;\n                default:\n                    return;\n            }\n            if (_this.props.restrictPosition) {\n                newCrop = restrictPosition(newCrop, _this.mediaSize, _this.state.cropSize, zoom, rotation);\n            }\n            if (!event.repeat) {\n                (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            }\n            onCropChange(newCrop);\n        };\n        _this.onKeyUp = function(event) {\n            var _a, _b;\n            switch(event.key){\n                case \"ArrowUp\":\n                case \"ArrowDown\":\n                case \"ArrowLeft\":\n                case \"ArrowRight\":\n                    event.preventDefault();\n                    break;\n                default:\n                    return;\n            }\n            _this.emitCropData();\n            (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n        };\n        return _this;\n    }\n    Cropper.prototype.componentDidMount = function() {\n        if (!this.currentDoc || !this.currentWindow) return;\n        if (this.containerRef) {\n            if (this.containerRef.ownerDocument) {\n                this.currentDoc = this.containerRef.ownerDocument;\n            }\n            if (this.currentDoc.defaultView) {\n                this.currentWindow = this.currentDoc.defaultView;\n            }\n            this.initResizeObserver();\n            // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n            if (typeof window.ResizeObserver === \"undefined\") {\n                this.currentWindow.addEventListener(\"resize\", this.computeSizes);\n            }\n            this.props.zoomWithScroll && this.containerRef.addEventListener(\"wheel\", this.onWheel, {\n                passive: false\n            });\n            this.containerRef.addEventListener(\"gesturestart\", this.onGestureStart);\n        }\n        this.currentDoc.addEventListener(\"scroll\", this.onScroll);\n        if (!this.props.disableAutomaticStylesInjection) {\n            this.styleRef = this.currentDoc.createElement(\"style\");\n            this.styleRef.setAttribute(\"type\", \"text/css\");\n            if (this.props.nonce) {\n                this.styleRef.setAttribute(\"nonce\", this.props.nonce);\n            }\n            this.styleRef.innerHTML = css_248z;\n            this.currentDoc.head.appendChild(this.styleRef);\n        }\n        // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n        if (this.imageRef.current && this.imageRef.current.complete) {\n            this.onMediaLoad();\n        }\n        // set image and video refs in the parent if the callbacks exist\n        if (this.props.setImageRef) {\n            this.props.setImageRef(this.imageRef);\n        }\n        if (this.props.setVideoRef) {\n            this.props.setVideoRef(this.videoRef);\n        }\n        if (this.props.setCropperRef) {\n            this.props.setCropperRef(this.cropperRef);\n        }\n    };\n    Cropper.prototype.componentWillUnmount = function() {\n        var _a, _b;\n        if (!this.currentDoc || !this.currentWindow) return;\n        if (typeof window.ResizeObserver === \"undefined\") {\n            this.currentWindow.removeEventListener(\"resize\", this.computeSizes);\n        }\n        (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n        if (this.containerRef) {\n            this.containerRef.removeEventListener(\"gesturestart\", this.preventZoomSafari);\n        }\n        if (this.styleRef) {\n            (_b = this.styleRef.parentNode) === null || _b === void 0 ? void 0 : _b.removeChild(this.styleRef);\n        }\n        this.cleanEvents();\n        this.props.zoomWithScroll && this.clearScrollEvent();\n    };\n    Cropper.prototype.componentDidUpdate = function(prevProps) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n        if (prevProps.rotation !== this.props.rotation) {\n            this.computeSizes();\n            this.recomputeCropPosition();\n        } else if (prevProps.aspect !== this.props.aspect) {\n            this.computeSizes();\n        } else if (prevProps.objectFit !== this.props.objectFit) {\n            this.computeSizes();\n        } else if (prevProps.zoom !== this.props.zoom) {\n            this.recomputeCropPosition();\n        } else if (((_a = prevProps.cropSize) === null || _a === void 0 ? void 0 : _a.height) !== ((_b = this.props.cropSize) === null || _b === void 0 ? void 0 : _b.height) || ((_c = prevProps.cropSize) === null || _c === void 0 ? void 0 : _c.width) !== ((_d = this.props.cropSize) === null || _d === void 0 ? void 0 : _d.width)) {\n            this.computeSizes();\n        } else if (((_e = prevProps.crop) === null || _e === void 0 ? void 0 : _e.x) !== ((_f = this.props.crop) === null || _f === void 0 ? void 0 : _f.x) || ((_g = prevProps.crop) === null || _g === void 0 ? void 0 : _g.y) !== ((_h = this.props.crop) === null || _h === void 0 ? void 0 : _h.y)) {\n            this.emitCropAreaChange();\n        }\n        if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n            this.props.zoomWithScroll ? this.containerRef.addEventListener(\"wheel\", this.onWheel, {\n                passive: false\n            }) : this.clearScrollEvent();\n        }\n        if (prevProps.video !== this.props.video) {\n            (_j = this.videoRef.current) === null || _j === void 0 ? void 0 : _j.load();\n        }\n        var objectFit = this.getObjectFit();\n        if (objectFit !== this.state.mediaObjectFit) {\n            this.setState({\n                mediaObjectFit: objectFit\n            }, this.computeSizes);\n        }\n    };\n    Cropper.prototype.getAspect = function() {\n        var _a = this.props, cropSize = _a.cropSize, aspect = _a.aspect;\n        if (cropSize) {\n            return cropSize.width / cropSize.height;\n        }\n        return aspect;\n    };\n    Cropper.prototype.getObjectFit = function() {\n        var _a, _b, _c, _d;\n        if (this.props.objectFit === \"cover\") {\n            var mediaRef = this.imageRef.current || this.videoRef.current;\n            if (mediaRef && this.containerRef) {\n                this.containerRect = this.containerRef.getBoundingClientRect();\n                var containerAspect = this.containerRect.width / this.containerRect.height;\n                var naturalWidth = ((_a = this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n                var naturalHeight = ((_c = this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n                var mediaAspect = naturalWidth / naturalHeight;\n                return mediaAspect < containerAspect ? \"horizontal-cover\" : \"vertical-cover\";\n            }\n            return \"horizontal-cover\";\n        }\n        return this.props.objectFit;\n    };\n    Cropper.prototype.onPinchStart = function(e) {\n        var pointA = Cropper.getTouchPoint(e.touches[0]);\n        var pointB = Cropper.getTouchPoint(e.touches[1]);\n        this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB);\n        this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB);\n        this.onDragStart(getCenter(pointA, pointB));\n    };\n    Cropper.prototype.onPinchMove = function(e) {\n        var _this = this;\n        if (!this.currentDoc || !this.currentWindow) return;\n        var pointA = Cropper.getTouchPoint(e.touches[0]);\n        var pointB = Cropper.getTouchPoint(e.touches[1]);\n        var center = getCenter(pointA, pointB);\n        this.onDrag(center);\n        if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout);\n        this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(function() {\n            var distance = getDistanceBetweenPoints(pointA, pointB);\n            var newZoom = _this.props.zoom * (distance / _this.lastPinchDistance);\n            _this.setNewZoom(newZoom, center, {\n                shouldUpdatePosition: false\n            });\n            _this.lastPinchDistance = distance;\n            var rotation = getRotationBetweenPoints(pointA, pointB);\n            var newRotation = _this.props.rotation + (rotation - _this.lastPinchRotation);\n            _this.props.onRotationChange && _this.props.onRotationChange(newRotation);\n            _this.lastPinchRotation = rotation;\n        });\n    };\n    Cropper.prototype.render = function() {\n        var _this = this;\n        var _a;\n        var _b = this.props, image = _b.image, video = _b.video, mediaProps = _b.mediaProps, cropperProps = _b.cropperProps, transform = _b.transform, _c = _b.crop, x = _c.x, y = _c.y, rotation = _b.rotation, zoom = _b.zoom, cropShape = _b.cropShape, showGrid = _b.showGrid, _d = _b.style, containerStyle = _d.containerStyle, cropAreaStyle = _d.cropAreaStyle, mediaStyle = _d.mediaStyle, _e = _b.classes, containerClassName = _e.containerClassName, cropAreaClassName = _e.cropAreaClassName, mediaClassName = _e.mediaClassName;\n        var objectFit = (_a = this.state.mediaObjectFit) !== null && _a !== void 0 ? _a : this.getObjectFit();\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            onMouseDown: this.onMouseDown,\n            onTouchStart: this.onTouchStart,\n            ref: function ref(el) {\n                return _this.containerRef = el;\n            },\n            \"data-testid\": \"container\",\n            style: containerStyle,\n            className: classNames(\"reactEasyCrop_Container\", containerClassName)\n        }, image ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n            alt: \"\",\n            className: classNames(\"reactEasyCrop_Image\", objectFit === \"contain\" && \"reactEasyCrop_Contain\", objectFit === \"horizontal-cover\" && \"reactEasyCrop_Cover_Horizontal\", objectFit === \"vertical-cover\" && \"reactEasyCrop_Cover_Vertical\", mediaClassName)\n        }, mediaProps, {\n            src: image,\n            ref: this.imageRef,\n            style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, mediaStyle), {\n                transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n            }),\n            onLoad: this.onMediaLoad\n        })) : video && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"video\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n            autoPlay: true,\n            playsInline: true,\n            loop: true,\n            muted: true,\n            className: classNames(\"reactEasyCrop_Video\", objectFit === \"contain\" && \"reactEasyCrop_Contain\", objectFit === \"horizontal-cover\" && \"reactEasyCrop_Cover_Horizontal\", objectFit === \"vertical-cover\" && \"reactEasyCrop_Cover_Vertical\", mediaClassName)\n        }, mediaProps, {\n            ref: this.videoRef,\n            onLoadedMetadata: this.onMediaLoad,\n            style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, mediaStyle), {\n                transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n            }),\n            controls: false\n        }), (Array.isArray(video) ? video : [\n            {\n                src: video\n            }\n        ]).map(function(item) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"source\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n                key: item.src\n            }, item));\n        })), this.state.cropSize && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n            ref: this.cropperRef,\n            style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, cropAreaStyle), {\n                width: this.state.cropSize.width,\n                height: this.state.cropSize.height\n            }),\n            tabIndex: 0,\n            onKeyDown: this.onKeyDown,\n            onKeyUp: this.onKeyUp,\n            \"data-testid\": \"cropper\",\n            className: classNames(\"reactEasyCrop_CropArea\", cropShape === \"round\" && \"reactEasyCrop_CropAreaRound\", showGrid && \"reactEasyCrop_CropAreaGrid\", cropAreaClassName)\n        }, cropperProps)));\n    };\n    Cropper.defaultProps = {\n        zoom: 1,\n        rotation: 0,\n        aspect: 4 / 3,\n        maxZoom: MAX_ZOOM,\n        minZoom: MIN_ZOOM,\n        cropShape: \"rect\",\n        objectFit: \"contain\",\n        showGrid: true,\n        style: {},\n        classes: {},\n        mediaProps: {},\n        cropperProps: {},\n        zoomSpeed: 1,\n        restrictPosition: true,\n        zoomWithScroll: true,\n        keyboardStep: KEYBOARD_STEP\n    };\n    Cropper.getMousePoint = function(e) {\n        return {\n            x: Number(e.clientX),\n            y: Number(e.clientY)\n        };\n    };\n    Cropper.getTouchPoint = function(touch) {\n        return {\n            x: Number(touch.clientX),\n            y: Number(touch.clientY)\n        };\n    };\n    return Cropper;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n //# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-easy-crop/index.module.js\n");

/***/ })

};
;