import { ConnectDB } from "@/lib/config/db";
import FavoriteModel from "@/lib/models/FavoriteModel";
import BlogModel from "@/lib/models/BlogModel";
import { NextResponse } from "next/server";
import { verifyToken } from "@/lib/utils/token";

// Connect to database
ConnectDB();

// Helper function to extract user ID from token
const getUserFromToken = (request) => {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log("Missing or invalid Authorization header");
      return null;
    }
    
    const token = authHeader.split(' ')[1];
    if (!token) {
      console.log("No token found in Authorization header");
      return null;
    }
    
    const userData = verifyToken(token);
    if (!userData || !userData.userId) {
      console.log("Invalid token payload:", userData);
      return null;
    }
    
    return userData;
  } catch (error) {
    console.error("Error extracting user from token:", error);
    return null;
  }
};

// Get user's favorites
export async function GET(request) {
  try {
    // Verify authentication
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    // Get query parameters
    const blogId = request.nextUrl.searchParams.get('blogId');
    
    // If blogId is provided, check if this specific blog is favorited
    if (blogId) {
      const favorite = await FavoriteModel.findOne({ 
        userId: userData.userId, 
        blogId 
      });
      
      return NextResponse.json({ 
        success: true, 
        isFavorite: !!favorite 
      });
    }
    
    // Otherwise, get all favorites with blog details
    const favorites = await FavoriteModel.find({ userId: userData.userId })
      .sort({ createdAt: -1 });
      
    // Get full blog details for each favorite
    const blogIds = favorites.map(fav => fav.blogId);
    const blogs = await BlogModel.find({ _id: { $in: blogIds } });
    
    // Map blogs to include favorite information
    const favoritedBlogs = blogs.map(blog => {
      const favorite = favorites.find(f => f.blogId.toString() === blog._id.toString());
      return {
        ...blog.toObject(),
        favoriteId: favorite._id,
        favoritedAt: favorite.createdAt
      };
    });
    
    return NextResponse.json({ 
      success: true, 
      favorites: favoritedBlogs 
    });
    
  } catch (error) {
    console.error("Error fetching favorites:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch favorites" 
    }, { status: 500 });
  }
}

// Add a favorite
export async function POST(request) {
  try {
    // Verify authentication
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const body = await request.json();
    const { blogId } = body;
    
    // Check if blog exists
    const blog = await BlogModel.findById(blogId);
    if (!blog) {
      return NextResponse.json({ success: false, message: "Blog not found" }, { status: 404 });
    }
    
    // Check if already favorited
    const existingFavorite = await FavoriteModel.findOne({ 
      userId: userData.userId, 
      blogId 
    });
    
    if (existingFavorite) {
      return NextResponse.json({ success: true, message: "Blog already in favorites" });
    }
    
    // Create new favorite
    await FavoriteModel.create({
      userId: userData.userId,
      blogId
    });
    
    return NextResponse.json({ 
      success: true, 
      message: "Blog added to favorites" 
    });
    
  } catch (error) {
    console.error("Error adding favorite:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to add favorite" 
    }, { status: 500 });
  }
}

// Remove a favorite
export async function DELETE(request) {
  try {
    // Verify authentication
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const blogId = request.nextUrl.searchParams.get('blogId');
    
    // Delete the favorite
    const result = await FavoriteModel.findOneAndDelete({ 
      userId: userData.userId, 
      blogId 
    });
    
    if (!result) {
      return NextResponse.json({ success: false, message: "Favorite not found" }, { status: 404 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: "Blog removed from favorites" 
    });
    
  } catch (error) {
    console.error("Error removing favorite:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to remove favorite" 
    }, { status: 500 });
  }
}




