'use client'
import { assets } from '@/Assets/assets';
import axios from 'axios';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const UserFavoritesPage = () => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const authToken = localStorage.getItem('authToken');
    const userId = localStorage.getItem('userId');
    
    if (!authToken || !userId) {
      router.push('/login');
      return;
    }
    
    fetchFavorites(authToken);
  }, [router]);

  const fetchFavorites = async (token) => {
    try {
      setLoading(true);
      const response = await axios.get('/api/favorites', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setFavorites(response.data.favorites || []);
      } else {
        toast.error("Failed to load favorites");
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
      toast.error("Failed to load favorites");
    } finally {
      setLoading(false);
    }
  };

  const removeFavorite = async (blogId) => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await axios.delete(`/api/favorites?blogId=${blogId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // Update the favorites list
        setFavorites(favorites.filter(blog => blog._id.toString() !== blogId));
        toast.success("Removed from favorites");
      } else {
        toast.error("Failed to remove from favorites");
      }
    } catch (error) {
      console.error("Error removing favorite:", error);
      toast.error("Failed to remove from favorites");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">My Favorite Blogs</h1>
          <Link href="/profile" className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300">
            Back to Profile
          </Link>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-16">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
          </div>
        ) : favorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favorites.map(blog => (
              <div key={blog._id} className="bg-white rounded-lg overflow-hidden shadow-md">
                <div className="relative h-48">
                  <Image 
                    src={blog.image || assets.placeholder} 
                    alt={blog.title} 
                    fill 
                    className="object-cover"
                  />
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <span className="inline-block px-2 py-1 text-xs bg-gray-100 rounded-full">
                      {blog.category}
                    </span>
                    <button 
                      onClick={() => removeFavorite(blog._id)}
                      className="text-yellow-500 hover:text-yellow-700"
                      title="Remove from favorites"
                    >
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="currentColor"
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                      </svg>
                    </button>
                  </div>
                  <h2 className="text-lg font-semibold mb-2 line-clamp-2">{blog.title}</h2>
                  <div 
                    className="text-sm text-gray-600 mb-3 line-clamp-3"
                    dangerouslySetInnerHTML={{
                      __html: blog.description.substring(0, 120) + '...'
                    }}
                  />
                  <Link 
                    href={`/blogs/${blog._id}`}
                    className="inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition"
                  >
                    Read Article
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-5xl mb-4">⭐</div>
            <h2 className="text-xl font-semibold mb-2">No favorites yet</h2>
            <p className="text-gray-600 mb-6">Start adding blogs to your favorites by clicking the star icon on blog posts.</p>
            <Link 
              href="/"
              className="inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition"
            >
              Browse Blogs
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserFavoritesPage;
