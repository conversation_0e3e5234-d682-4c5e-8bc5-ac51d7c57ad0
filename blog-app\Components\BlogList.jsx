import React, { useEffect, useState, useRef } from 'react'
import BlogItem from './BlogItem'
import axios from 'axios';

const BLOGS_PER_PAGE = 12;

const BlogList = ({ searchTerm = "" }) => {
    const [menu, setMenu] = useState("All");
    const [blogs, setBlogs] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
    const [isMounted, setIsMounted] = useState(false);
    const dropdownRef = useRef(null);

    // Fetch data
    const fetchBlogs = async () => {
        try {
            const response = await axios.get('/api/blog');
            if (response.data.blogs) {
                setBlogs(response.data.blogs);
            } else {
                // Handle case where API returns error but with 200 status
                setBlogs([]);
            }
            setLoading(false);
        } catch (error) {
            console.error("Error fetching blogs:", error);
            setBlogs([]); // Set empty array on error
            setLoading(false);
        }
    }

    const fetchCategories = async () => {
        try {
            const response = await axios.get('/api/categories');
            if (response.data.success && response.data.categories.length > 0) {
                setCategories(response.data.categories);
            } else {
                // Fallback to default categories if none found
                setCategories([
                    { _id: '1', name: 'Startup' },
                    { _id: '2', name: 'Technology' },
                    { _id: '3', name: 'Lifestyle' }
                ]);
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
            // Fallback to default categories on error
            setCategories([
                { _id: '1', name: 'Startup' },
                { _id: '2', name: 'Technology' },
                { _id: '3', name: 'Lifestyle' }
            ]);
        }
    };

    // Set mounted state to prevent hydration mismatch
    useEffect(() => {
        setIsMounted(true);
        fetchBlogs();
        fetchCategories();
    }, []);

    // Reset to first page when menu changes
    useEffect(() => {
        setCurrentPage(1);
    }, [menu]);

    // Close dropdown when clicking outside
    useEffect(() => {
        if (!isMounted) return;
        
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowCategoryDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isMounted]);

    // Filter blogs by category and search term
    const filteredBlogs = blogs.filter((item) => {
        const matchesCategory = menu === "All" ? true : item.category === menu;
        const matchesSearch = searchTerm.trim() === "" ? true : (
            item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        return matchesCategory && matchesSearch;
    });
    const totalPages = Math.ceil(filteredBlogs.length / BLOGS_PER_PAGE);
    const startIdx = (currentPage - 1) * BLOGS_PER_PAGE;
    const currentBlogs = filteredBlogs.slice(startIdx, startIdx + BLOGS_PER_PAGE);

    const handleCategorySelect = (categoryName) => {
        setMenu(categoryName);
        setShowCategoryDropdown(false);
    };

    // Return a loading state until client-side hydration is complete
    if (!isMounted) {
        return <div className="text-center py-10">Loading...</div>;
    }

    return (
        <div>
            <div className='flex justify-center my-10'>
                <div className='flex gap-4'>
                    <button 
                        onClick={() => setMenu('All')} 
                        className={`py-2 px-6 rounded-md transition-colors ${
                            menu === "All" 
                                ? 'bg-black text-white' 
                                : 'bg-gray-100 hover:bg-gray-200'
                        }`}
                    >
                        All
                    </button>
                    
                    <div className="relative" ref={dropdownRef}>
                        <button 
                            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
                            className={`py-2 px-6 rounded-md flex items-center gap-2 transition-colors ${
                                menu !== "All" 
                                    ? 'bg-black text-white' 
                                    : 'bg-gray-100 hover:bg-gray-200'
                            }`}
                        >
                            {menu !== "All" ? menu : "Categories"}
                            <svg 
                                xmlns="http://www.w3.org/2000/svg" 
                                className={`h-4 w-4 transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} 
                                fill="none" 
                                viewBox="0 0 24 24" 
                                stroke="currentColor"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        
                        {showCategoryDropdown && (
                            <div className="absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 max-h-60 overflow-auto">
                                {categories.map(category => (
                                    <button 
                                        key={category._id}
                                        onClick={() => handleCategorySelect(category.name)}
                                        className={`block w-full text-left px-4 py-2 text-sm ${
                                            menu === category.name 
                                                ? 'bg-gray-100 font-medium' 
                                                : 'hover:bg-gray-50'
                                        }`}
                                    >
                                        {category.name}
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
            
            {loading ? (
                <div className="text-center py-10">Loading blogs...</div>
            ) : (
                <>
                    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24'>
                        {currentBlogs.map((item, index) => (
                            <BlogItem 
                                key={item._id || index} 
                                id={item._id} 
                                image={item.image} 
                                title={item.title} 
                                description={item.description} 
                                category={item.category} 
                            />
                        ))}
                    </div>
                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex justify-center mt-0 mb-8 gap-2">
                            {Array.from({ length: totalPages }, (_, i) => (
                                <button
                                    key={i}
                                    onClick={() => setCurrentPage(i + 1)}
                                    className={`px-4 py-2 border border-black rounded ${currentPage === i + 1 ? 'bg-black text-white' : 'bg-white text-black hover:bg-gray-100'}`}
                                >
                                    {i + 1}
                                </button>
                            ))}
                        </div>
                    )}
                </>
            )}
        </div>
    )
}

export default BlogList
