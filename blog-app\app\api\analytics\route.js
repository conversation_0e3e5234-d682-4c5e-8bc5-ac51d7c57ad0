import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import AnalyticsModel from '@/lib/models/AnalyticsModel';
import crypto from 'crypto';
import { verifyToken } from '@/lib/utils/token';

// Helper function to get user from token
const getUserFromToken = (request) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    const token = authHeader.split(' ')[1];
    if (!token) {
      return null;
    }
    
    // Use the verifyToken function from your token utility
    return verifyToken(token);
  } catch (error) {
    console.error('Error getting user from token:', error);
    return null;
  }
};

// Track page view
export async function POST(request) {
  try {
    await ConnectDB();
    
    const body = await request.json();
    const { path, contentType, blogId, referrer } = body;
    
    // Get user info if logged in
    const userData = getUserFromToken(request);
    const userId = userData?.userId || null;
    
    // Get IP address and hash it for privacy
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               '127.0.0.1';
    
    const ipHash = crypto
      .createHash('sha256')
      .update(ip + (process.env.IP_SALT || 'salt'))
      .digest('hex');
    
    // Get user agent
    const userAgent = request.headers.get('user-agent') || '';
    
    // Create analytics entry
    await AnalyticsModel.create({
      path,
      contentType,
      blogId,
      userId,
      ipHash,
      userAgent,
      referrer,
      timestamp: new Date()
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error tracking analytics:', error);
    return NextResponse.json({ success: false, message: 'Failed to track analytics' }, { status: 500 });
  }
}

// Get analytics data
export async function GET(request) {
  try {
    await ConnectDB();
    
    // Verify admin access
    const userData = getUserFromToken(request);
    console.log('User data from token:', userData);
    
    if (!userData || userData.role !== 'admin') {
      console.log('Unauthorized access attempt to analytics');
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '7days'; // Default to 7 days
    const blogId = searchParams.get('blogId'); // Optional blog ID filter
    
    console.log('Fetching analytics for period:', period);
    
    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '24hours':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7days':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }
    
    // Base query with date range
    const query = {
      timestamp: { $gte: startDate, $lte: endDate }
    };
    
    // Add blog ID filter if provided
    if (blogId) {
      query.blogId = blogId;
    }
    
    console.log('Analytics query:', JSON.stringify(query));
    
    // Get total page views
    const totalViews = await AnalyticsModel.countDocuments(query);
    console.log('Total views:', totalViews);
    
    // Get unique visitors (by IP hash)
    const uniqueVisitors = await AnalyticsModel.distinct('ipHash', query).then(ips => ips.length);
    console.log('Unique visitors:', uniqueVisitors);
    
    // Get views by content type
    const viewsByType = await AnalyticsModel.aggregate([
      { $match: query },
      { $group: { _id: '$contentType', count: { $sum: 1 } } }
    ]);
    console.log('Views by type:', viewsByType);
    
    // Get top pages/posts
    const topPages = await AnalyticsModel.aggregate([
      { $match: query },
      { $group: { _id: '$path', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    console.log('Top pages:', topPages);
    
    // Get traffic over time (daily)
    const trafficOverTime = await AnalyticsModel.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    console.log('Traffic over time:', trafficOverTime);
    
    // Get referrers
    const topReferrers = await AnalyticsModel.aggregate([
      { $match: { ...query, referrer: { $ne: null, $ne: '' } } },
      { $group: { _id: '$referrer', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    console.log('Top referrers:', topReferrers);
    
    const responseData = {
      success: true,
      data: {
        totalViews,
        uniqueVisitors,
        viewsByType,
        topPages,
        trafficOverTime,
        topReferrers
      }
    };
    
    console.log('Analytics response data:', JSON.stringify(responseData));
    
    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json({ success: false, message: 'Failed to fetch analytics' }, { status: 500 });
  }
}







