import mongoose from 'mongoose';

const AnalyticsSchema = new mongoose.Schema({
  // Page or post being viewed
  path: {
    type: String,
    required: true,
    index: true
  },
  // Type of content (blog, page, etc.)
  contentType: {
    type: String,
    required: true,
    enum: ['blog', 'page', 'home', 'other'],
    default: 'other'
  },
  // Associated blog ID if applicable
  blogId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'blogs',
    index: true
  },
  // User ID if logged in
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    index: true
  },
  // IP address (hashed for privacy)
  ipHash: {
    type: String,
    index: true
  },
  // User agent info
  userAgent: String,
  // Referrer URL
  referrer: String,
  // Timestamp
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  }
});

// Create compound indexes for efficient querying
AnalyticsSchema.index({ path: 1, timestamp: 1 });
AnalyticsSchema.index({ contentType: 1, timestamp: 1 });
AnalyticsSchema.index({ blogId: 1, timestamp: 1 });

// Check if model exists before creating
const AnalyticsModel = mongoose.models.analytics || mongoose.model('analytics', AnalyticsSchema);

export default AnalyticsModel;



