import mongoose from 'mongoose';

const LikeSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true
  },
  blogId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Blog',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create a compound index to ensure a user can only like a blog once
LikeSchema.index({ userId: 1, blogId: 1 }, { unique: true });

export default mongoose.models.Like || mongoose.model('Like', LikeSchema);