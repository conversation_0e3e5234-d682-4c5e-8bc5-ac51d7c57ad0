'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import Link from 'next/link';
import Image from 'next/image';
import axios from 'axios';
import { assets } from '@/Assets/assets';
import { getContentPreview } from '@/utils/textUtils';
import Cropper from 'react-easy-crop';

const ProfilePage = () => {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [userData, setUserData] = useState({
        name: '',
        email: '',
        profilePicture: '/default_profile.png'
    });
    const [previewUrl, setPreviewUrl] = useState(null);
    const [activeTab, setActiveTab] = useState('profile');
    const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
    const [newProfilePicture, setNewProfilePicture] = useState(null);
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [showPasswordForm, setShowPasswordForm] = useState(false);
    const [favorites, setFavorites] = useState([]);
    const [favoritesLoading, setFavoritesLoading] = useState(false);
    const [saveSuccess, setSaveSuccess] = useState(false);
    
    // Add new state for image cropping
    const [showCropper, setShowCropper] = useState(false);
    const [cropImage, setCropImage] = useState(null);
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
    const [croppedImage, setCroppedImage] = useState(null);
    
    // Add logout functions
    const handleLogoutClick = () => {
        setShowLogoutConfirm(true);
    };

    const handleLogoutConfirm = () => {
        // Clear auth data from localStorage
        localStorage.removeItem('authToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userId');
        localStorage.removeItem('userName');
        localStorage.removeItem('userProfilePicture');
        
        toast.success("Logged out successfully");
        
        // Add delay before navigation
        setTimeout(() => {
            router.push('/');
        }, 300);
    };

    const handleLogoutCancel = () => {
        setShowLogoutConfirm(false);
    };
    
    useEffect(() => {
        // Check if user is logged in
        const authToken = localStorage.getItem('authToken');
        const userId = localStorage.getItem('userId');
        
        if (!authToken || !userId) {
            toast.error("Please log in to view your profile");
            router.push('/');
            return;
        }
        
        // Fetch user profile data
        const fetchUserProfile = async () => {
            try {
                const response = await axios.get(`/api/profile?userId=${userId}`);
                
                if (response.data.success) {
                    setUserData({
                        id: response.data.user.id,
                        email: response.data.user.email,
                        name: response.data.user.name || '',
                        role: response.data.user.role, // Make sure we store the role
                        profilePicture: response.data.user.profilePicture
                    });
                } else {
                    toast.error(response.data.message || "Failed to load profile");
                }
            } catch (error) {
                console.error("Profile fetch error:", error);
                toast.error("Failed to load profile data");
            } finally {
                setLoading(false);
            }
        };
        
        fetchUserProfile();
        
        // If favorites tab is active, fetch favorites
        if (activeTab === 'favorites') {
            fetchFavorites(authToken);
        }
    }, [router, activeTab]);

    const fetchFavorites = async (token) => {
        try {
            setFavoritesLoading(true);
            const response = await axios.get('/api/favorites', {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (response.data.success) {
                setFavorites(response.data.favorites || []);
            } else {
                toast.error("Failed to load favorites");
            }
        } catch (error) {
            console.error("Error fetching favorites:", error);
            toast.error("Failed to load favorites");
        } finally {
            setFavoritesLoading(false);
        }
    };

    const removeFavorite = async (blogId) => {
        try {
            const token = localStorage.getItem('authToken');
            const response = await axios.delete(`/api/favorites?blogId=${blogId}`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (response.data.success) {
                // Update the favorites list
                setFavorites(favorites.filter(blog => blog._id.toString() !== blogId));
                toast.success("Removed from favorites");
            } else {
                toast.error("Failed to remove from favorites");
            }
        } catch (error) {
            console.error("Error removing favorite:", error);
            toast.error("Failed to remove from favorites");
        }
    };

    const handleNameChange = (e) => {
        setUserData({...userData, name: e.target.value});
    };

    const handleProfilePictureChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Instead of directly setting the file, show the cropper
            const reader = new FileReader();
            reader.onloadend = () => {
                setCropImage(reader.result);
                setShowCropper(true);
            };
            reader.readAsDataURL(file);
        }
    };
    
    const onCropComplete = (croppedArea, croppedAreaPixels) => {
        setCroppedAreaPixels(croppedAreaPixels);
    };
    
    const getCroppedImg = async (imageSrc, pixelCrop) => {
        const image = new Image();
        image.src = imageSrc;
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = pixelCrop.width;
        canvas.height = pixelCrop.height;
        
        ctx.drawImage(
            image,
            pixelCrop.x,
            pixelCrop.y,
            pixelCrop.width,
            pixelCrop.height,
            0,
            0,
            pixelCrop.width,
            pixelCrop.height
        );
        
        return new Promise((resolve) => {
            canvas.toBlob((blob) => {
                resolve(blob);
            }, 'image/jpeg');
        });
    };
    
    const applyCrop = async () => {
        try {
            if (!croppedAreaPixels) return;
            
            const croppedBlob = await getCroppedImg(
                cropImage,
                croppedAreaPixels
            );
            
            const croppedImageUrl = URL.createObjectURL(croppedBlob);
            setPreviewUrl(croppedImageUrl);
            
            // Convert blob to file
            const file = new File([croppedBlob], "cropped_profile.jpg", { type: "image/jpeg" });
            setNewProfilePicture(file);
            
            setShowCropper(false);
        } catch (e) {
            console.error('Error applying crop:', e);
            toast.error('Failed to crop image');
        }
    };
    
    const cancelCrop = () => {
        setShowCropper(false);
        setCropImage(null);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaveSuccess(false); // Reset success state
        
        try {
            const formData = new FormData();
            formData.append('userId', userData.id);
            formData.append('name', userData.name);
            
            if (newProfilePicture) {
                formData.append('profilePicture', newProfilePicture);
            }
            
            const response = await axios.put('/api/profile', formData);
            
            if (response.data.success) {
                // Update local storage with new profile data
                localStorage.setItem('userName', response.data.user.name);
                localStorage.setItem('userProfilePicture', response.data.user.profilePicture);
                
                // Update state with new data
                setUserData({
                    ...userData,
                    name: response.data.user.name,
                    profilePicture: response.data.user.profilePicture
                });
                
                // Clear file input
                setNewProfilePicture(null);
                setPreviewUrl(null);
                
                // Set success state to show message
                setSaveSuccess(true);
                
                // Show toast
                toast.success("Profile updated successfully");
                
                // Refresh the page after a 1-second delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                toast.error(response.data.message || "Failed to update profile");
            }
        } catch (error) {
            console.error("Profile update error:", error);
            toast.error("Failed to update profile");
        }
    };

    // Handle password form input changes
    const handlePasswordChange = (e) => {
        setPasswordData({
            ...passwordData,
            [e.target.name]: e.target.value
        });
    };
    
    // Handle password form submission
    const handlePasswordSubmit = async (e) => {
        e.preventDefault();
        
        // Validate passwords match
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            toast.error("New passwords do not match");
            return;
        }
        
        try {
            const response = await axios.put('/api/password', {
                userId: userData.id,
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword
            });
            
            if (response.data.success) {
                toast.success("Password updated successfully");
                // Reset form
                setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                });
                // Hide form
                setShowPasswordForm(false);
            } else {
                toast.error(response.data.message || "Failed to update password");
            }
        } catch (error) {
            console.error("Password update error:", error);
            toast.error(error.response?.data?.message || "Failed to update password");
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex justify-center items-center">
                <p>Loading profile...</p>
            </div>
        );
    }

    return (
        <>
            {/* Header Section (copied and adapted from About Us) */}
            <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
                <div className='flex justify-between items-center'>
                    <Link href='/'>
                        <Image src={assets.logo} width={180} alt='Mr.Blogger' className='w-[130px] sm:w-auto' />
                    </Link>
                    <Link href='/'>
                        <button className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'>
                            Back to Home
                        </button>
                    </Link>
                </div>
                <div className='text-center my-16'>
                    <h1 className='text-3xl sm:text-5xl font-semibold'>Your Profile</h1>
                    <p className='mt-4 text-gray-600'>Manage your account and preferences</p>
                </div>
            </div>

            {/* Main Profile Content */}
            <div className="min-h-screen bg-gray-100 py-8">
                <div className="container mx-auto px-4 max-w-4xl">
                    <div className="bg-white p-6 rounded-lg shadow-md mb-8">
                        <div className="mb-6">
                            <div className="flex border-b border-gray-300">
                                <button 
                                    onClick={() => setActiveTab('profile')}
                                    className={`py-3 px-6 font-medium ${
                                        activeTab === 'profile' 
                                            ? 'border-b-2 border-black text-black' 
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    Profile
                                </button>
                                <button 
                                    onClick={() => setActiveTab('favorites')}
                                    className={`py-3 px-6 font-medium ${
                                        activeTab === 'favorites' 
                                            ? 'border-b-2 border-black text-black' 
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    My Favorites
                                </button>
                            </div>
                        </div>
                        
                        {activeTab === 'profile' ? (
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="flex flex-col items-center mb-8">
                                    <div className="relative mb-4">
                                        <Image 
                                            src={previewUrl || userData.profilePicture} 
                                            width={150} 
                                            height={150} 
                                            alt="Profile" 
                                            className="rounded-full object-cover w-[150px] h-[150px] border-4 border-gray-200"
                                        />
                                        <label 
                                            htmlFor="profilePicture" 
                                            className="absolute bottom-0 right-0 bg-black text-white p-2 rounded-full cursor-pointer"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                                                <circle cx="12" cy="13" r="4"></circle>
                                            </svg>
                                        </label>
                                        <input 
                                            type="file" 
                                            id="profilePicture" 
                                            onChange={handleProfilePictureChange} 
                                            className="hidden" 
                                            accept="image/*"
                                        />
                                    </div>
                                    <p className="text-sm text-gray-500">Click the camera icon to change your profile picture</p>
                                </div>
                                
                                <div>
                                    <label className="block text-gray-700 font-medium mb-2">Email</label>
                                    <input 
                                        type="email" 
                                        value={userData.email} 
                                        className="w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-100" 
                                        disabled 
                                    />
                                    <p className="mt-1 text-sm text-gray-500">Email cannot be changed</p>
                                </div>
                                
                                <div>
                                    <label className="block text-gray-700 font-medium mb-2">Display Name</label>
                                    <input 
                                        type="text" 
                                        value={userData.name} 
                                        onChange={handleNameChange} 
                                        className="w-full px-4 py-3 border border-gray-300 rounded-md" 
                                        placeholder="Enter your name"
                                    />
                                </div>
                                
                                <div className="flex flex-wrap gap-4">
                                    {saveSuccess && (
                                        <div className="fixed top-5 right-5 p-4 bg-white border-l-4 border-green-500 text-green-700 rounded shadow-lg z-50 max-w-xs animate-fade-in">
                                            <div className="flex items-center">
                                                <div className="mr-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <p className="font-bold">Success!</p>
                                                    <p className="text-sm">Profile updated successfully.</p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    <button 
                                        type="submit" 
                                        className="px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors"
                                    >
                                        Save Changes
                                    </button>
                                    
                                    <button 
                                        type="button" 
                                        onClick={() => setShowPasswordForm(!showPasswordForm)}
                                        className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors"
                                    >
                                        Change Password
                                    </button>
                                    
                                    {userData.role === 'admin' && (
                                        <button 
                                            type="button" 
                                            onClick={(e) => {
                                                e.preventDefault(); // Prevent form submission
                                                router.push('/admin');
                                            }}
                                            className="px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors"
                                        >
                                            Dashboard
                                        </button>
                                    )}
                                    
                                    <button 
                                        type="button" 
                                        onClick={(e) => {
                                            e.preventDefault(); // Prevent form submission
                                            handleLogoutClick();
                                        }}
                                        className="px-6 py-3 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors"
                                    >
                                        Logout
                                    </button>
                                    
                                    <button 
                                        type="button" 
                                        onClick={(e) => {
                                            e.preventDefault(); // Prevent form submission
                                            router.push('/');
                                        }}
                                        className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        ) : (
                            // Favorites tab content
                            <div>
                                {favoritesLoading ? (
                                    <div className="flex justify-center items-center py-16">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
                                    </div>
                                ) : favorites.length > 0 ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {favorites.map(blog => (
                                            <div key={blog._id} className="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                                                <div className="relative h-48">
                                                    <Image 
                                                        src={blog.image || assets.placeholder} 
                                                        alt={blog.title} 
                                                        fill 
                                                        className="object-cover"
                                                    />
                                                </div>
                                                <div className="p-4">
                                                    <div className="flex justify-between items-start mb-2">
                                                        <span className="inline-block px-2 py-1 text-xs bg-gray-100 rounded-full">
                                                            {blog.category}
                                                        </span>
                                                        <button 
                                                            onClick={() => removeFavorite(blog._id)}
                                                            className="text-yellow-500 hover:text-yellow-700"
                                                            title="Remove from favorites"
                                                        >
                                                            <svg 
                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                width="20" 
                                                                height="20" 
                                                                viewBox="0 0 24 24" 
                                                                fill="currentColor"
                                                                stroke="currentColor" 
                                                                strokeWidth="2" 
                                                                strokeLinecap="round" 
                                                                strokeLinejoin="round"
                                                            >
                                                                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                    <h2 className="text-lg font-semibold mb-2 line-clamp-2">{blog.title}</h2>
                                                    <div
                                                        className="text-sm text-gray-600 mb-3 line-clamp-3"
                                                        dangerouslySetInnerHTML={{
                                                            __html: getContentPreview(blog.description, 120)
                                                        }}
                                                    />
                                                    <Link 
                                                        href={`/blogs/${blog._id}`}
                                                        className="inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition"
                                                    >
                                                        Read Article
                                                    </Link>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-white rounded-lg p-8 text-center">
                                        <div className="text-5xl mb-4">⭐</div>
                                        <h2 className="text-xl font-semibold mb-2">No favorites yet</h2>
                                        <p className="text-gray-600 mb-6">Start adding blogs to your favorites by clicking the star icon on blog posts.</p>
                                        <Link 
                                            href="/"
                                            className="inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition"
                                        >
                                            Browse Blogs
                                        </Link>
                                    </div>
                                )}
                            </div>
                        )}
                        
                        {/* Password change form - shown conditionally */}
                        {showPasswordForm && activeTab === 'profile' && (
                            <div className="mt-8 pt-6 border-t border-gray-200">
                                <h2 className="text-xl font-semibold mb-4">Change Password</h2>
                                <form onSubmit={handlePasswordSubmit} className="space-y-6">
                                    <div>
                                        <label className="block text-gray-700 font-medium mb-2">Current Password</label>
                                        <input 
                                            type="password"
                                            name="currentPassword"
                                            value={passwordData.currentPassword}
                                            onChange={handlePasswordChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-md"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label className="block text-gray-700 font-medium mb-2">New Password</label>
                                        <input 
                                            type="password"
                                            name="newPassword"
                                            value={passwordData.newPassword}
                                            onChange={handlePasswordChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-md"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label className="block text-gray-700 font-medium mb-2">Confirm New Password</label>
                                        <input 
                                            type="password"
                                            name="confirmPassword"
                                            value={passwordData.confirmPassword}
                                            onChange={handlePasswordChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-md"
                                            required
                                        />
                                    </div>
                                    
                                    <div className="flex gap-4">
                                        <button 
                                            type="submit"
                                            className="px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors"
                                        >
                                            Update Password
                                        </button>
                                        <button 
                                            type="button"
                                            onClick={() => setShowPasswordForm(false)}
                                            className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                            </div>
                        )}
                    </div>
                </div>
            </div>
            
            {/* Logout Confirmation Modal */}
            {showLogoutConfirm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-md shadow-lg max-w-sm w-full">
                        <h3 className="text-lg font-semibold mb-4">Confirm Logout</h3>
                        <p className="mb-6">Are you sure you want to log out? You will need to log in again to access your account.</p>
                        <div className="flex justify-end gap-3">
                            <button 
                                onClick={handleLogoutCancel}
                                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleLogoutConfirm}
                                className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            )}
            
            {/* Add the cropper modal */}
            {showCropper && (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className="bg-white p-6 rounded-lg max-w-2xl w-full">
                        <h3 className="text-xl font-semibold mb-4">Adjust Profile Picture</h3>
                        <div className="relative h-80 mb-4">
                            <Cropper
                                image={cropImage}
                                crop={crop}
                                zoom={zoom}
                                aspect={1}
                                cropShape="round"
                                onCropChange={setCrop}
                                onCropComplete={onCropComplete}
                                onZoomChange={setZoom}
                            />
                        </div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Zoom: {zoom.toFixed(1)}x
                            </label>
                            <input
                                type="range"
                                min={1}
                                max={3}
                                step={0.1}
                                value={zoom}
                                onChange={(e) => setZoom(parseFloat(e.target.value))}
                                className="w-full"
                            />
                        </div>
                        <div className="flex gap-3">
                            <button
                                type="button"
                                onClick={applyCrop}
                                className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
                            >
                                Apply
                            </button>
                            <button
                                type="button"
                                onClick={cancelCrop}
                                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ProfilePage;
