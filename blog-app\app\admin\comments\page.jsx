'use client'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'
import Image from 'next/image'
import Link from 'next/link'

const AdminCommentsPage = () => {
  const [comments, setComments] = useState([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [statistics, setStatistics] = useState({
    totalComments: 0,
    totalDeletedComments: 0,
    totalReactions: 0
  })
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [selectedBlog, setSelectedBlog] = useState('')
  const [blogs, setBlogs] = useState([])
  const [deleteLoading, setDeleteLoading] = useState({})

  // Fetch comments
  const fetchComments = async (page = 1) => {
    try {
      setLoading(true)
      const authToken = localStorage.getItem('authToken')
      
      const params = {
        page,
        limit: pagination.limit,
        sortBy,
        sortOrder
      }
      
      if (selectedBlog) {
        params.blogId = selectedBlog
      }

      const response = await axios.get('/api/admin/comments', {
        params,
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (response.data.success) {
        setComments(response.data.comments)
        setPagination(response.data.pagination)
        setStatistics(response.data.statistics)
      } else {
        toast.error('Failed to load comments')
      }
    } catch (error) {
      console.error('Error fetching comments:', error)
      if (error.response?.status === 403) {
        toast.error('Admin access required')
      } else {
        toast.error('Failed to load comments')
      }
    } finally {
      setLoading(false)
    }
  }

  // Fetch blogs for filter dropdown
  const fetchBlogs = async () => {
    try {
      const response = await axios.get('/api/blog')
      if (response.data.success) {
        setBlogs(response.data.blogs || [])
      }
    } catch (error) {
      console.error('Error fetching blogs:', error)
    }
  }

  // Delete comment
  const deleteComment = async (commentId) => {
    if (!confirm('Are you sure you want to delete this comment and all its replies? This action cannot be undone.')) {
      return
    }

    try {
      setDeleteLoading(prev => ({ ...prev, [commentId]: true }))
      const authToken = localStorage.getItem('authToken')
      
      const response = await axios.delete('/api/admin/comments', {
        params: { commentId },
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (response.data.success) {
        toast.success('Comment deleted successfully')
        // Remove comment from list
        setComments(prev => prev.filter(comment => comment._id !== commentId))
        // Update statistics
        setStatistics(prev => ({
          ...prev,
          totalComments: prev.totalComments - 1,
          totalDeletedComments: prev.totalDeletedComments + 1
        }))
      } else {
        toast.error(response.data.message || 'Failed to delete comment')
      }
    } catch (error) {
      console.error('Error deleting comment:', error)
      toast.error(error.response?.data?.message || 'Failed to delete comment')
    } finally {
      setDeleteLoading(prev => ({ ...prev, [commentId]: false }))
    }
  }

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Handle sort change
  const handleSortChange = (newSortBy) => {
    if (newSortBy === sortBy) {
      setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')
    } else {
      setSortBy(newSortBy)
      setSortOrder('desc')
    }
  }

  useEffect(() => {
    fetchComments()
  }, [sortBy, sortOrder, selectedBlog])

  useEffect(() => {
    fetchBlogs()
  }, [])

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>Comment Management</h1>
      
      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-700">Total Comments</h3>
          <p className="text-2xl font-bold text-blue-600">{statistics.totalComments}</p>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-700">Deleted Comments</h3>
          <p className="text-2xl font-bold text-red-600">{statistics.totalDeletedComments}</p>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-700">Total Reactions</h3>
          <p className="text-2xl font-bold text-green-600">{statistics.totalReactions}</p>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="mb-6 flex flex-wrap gap-4 items-center">
        {/* Blog Filter */}
        <select
          value={selectedBlog}
          onChange={(e) => setSelectedBlog(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md"
        >
          <option value="">All Blogs</option>
          {blogs.map(blog => (
            <option key={blog._id} value={blog._id}>
              {blog.title}
            </option>
          ))}
        </select>

        {/* Sort Controls */}
        <div className="flex gap-2">
          <button
            onClick={() => handleSortChange('createdAt')}
            className={`px-3 py-2 rounded-md ${
              sortBy === 'createdAt' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
          >
            Date {sortBy === 'createdAt' && (sortOrder === 'desc' ? '↓' : '↑')}
          </button>
          <button
            onClick={() => handleSortChange('likeCount')}
            className={`px-3 py-2 rounded-md ${
              sortBy === 'likeCount' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
          >
            Likes {sortBy === 'likeCount' && (sortOrder === 'desc' ? '↓' : '↑')}
          </button>
        </div>

        {/* Settings Link */}
        <Link 
          href="/admin/comments/settings"
          className="ml-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Comment Settings
        </Link>
      </div>

      {/* Comments Table */}
      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-2">Loading comments...</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600">No comments found.</p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User & Content
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Blog
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reactions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {comments.map((comment) => (
                  <tr key={comment._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <Image
                          src={comment.userId?.profilePicture || '/default_profile.png'}
                          alt={comment.userId?.name || 'User'}
                          width={40}
                          height={40}
                          className="rounded-full object-cover w-10 h-10"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">
                            {comment.userId?.name || 'Anonymous'}
                          </p>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-3">
                            {comment.content}
                          </p>
                          {comment.parentCommentId && (
                            <span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                              Reply
                            </span>
                          )}
                          {comment.replyCount > 0 && (
                            <span className="inline-block mt-1 ml-2 px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                              {comment.replyCount} replies
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Link 
                        href={`/blogs/${comment.blogId._id}`}
                        className="text-blue-600 hover:underline text-sm"
                        target="_blank"
                      >
                        {comment.blogId?.title || 'Unknown Blog'}
                      </Link>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {formatDate(comment.createdAt)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex gap-4 text-sm">
                        <span className="flex items-center gap-1 text-green-600">
                          👍 {comment.likeCount}
                        </span>
                        <span className="flex items-center gap-1 text-red-600">
                          👎 {comment.dislikeCount}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => deleteComment(comment._id)}
                        disabled={deleteLoading[comment._id]}
                        className="text-red-600 hover:text-red-900 text-sm font-medium disabled:opacity-50"
                      >
                        {deleteLoading[comment._id] ? 'Deleting...' : 'Delete'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex justify-center mt-6 gap-2">
              {Array.from({ length: pagination.pages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => fetchComments(i + 1)}
                  className={`px-3 py-2 border rounded ${
                    pagination.page === i + 1 
                      ? 'bg-black text-white border-black' 
                      : 'bg-white text-black border-gray-300 hover:bg-gray-100'
                  }`}
                >
                  {i + 1}
                </button>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default AdminCommentsPage
