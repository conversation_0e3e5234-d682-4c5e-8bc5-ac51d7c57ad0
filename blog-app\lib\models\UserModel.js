import mongoose from "mongoose";

const Schema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true
    },
    role: {
        type: String,
        default: 'user',
        enum: ['user', 'admin']
    },
    profilePicture: {
        type: String,
        default: '/default_profile.png'
    },
    name: {
        type: String,
        default: ''
    },
    date: {
        type: Date,
        default: Date.now()
    }
});

const UserModel = mongoose.models.user || mongoose.model('user', Schema);

export default UserModel;
