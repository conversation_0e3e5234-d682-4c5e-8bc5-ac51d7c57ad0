'use client'
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { usePathname } from 'next/navigation';

const EmailSubscriptionPopup = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Don't show popup on admin pages
    if (pathname && pathname.startsWith('/admin')) {
      return;
    }

    // Check if user has already subscribed (permanent dismissal)
    const hasSubscribed = localStorage.getItem('emailSubscribed');
    if (hasSubscribed === 'true') {
      return;
    }

    // Check if user has permanently dismissed the popup (closed it twice)
    const permanentlyDismissed = localStorage.getItem('emailPopupPermanentlyDismissed');
    if (permanentlyDismissed === 'true') {
      return;
    }

    // Check if user has closed the popup recently
    const lastClosedTime = localStorage.getItem('emailPopupLastClosed');
    const closeCount = parseInt(localStorage.getItem('emailPopupCloseCount') || '0');
    const now = Date.now();

    // If user has closed it once and it's been less than 5 minutes, don't show
    if (lastClosedTime && closeCount >= 1 && (now - parseInt(lastClosedTime)) < 300000) { // 5 minutes = 300000ms
      return;
    }

    // Determine the delay based on whether this is first time or after first close
    let delay;
    if (closeCount === 0) {
      // First time - show after 2 minutes
      delay = 120000; // 2 minutes = 120000ms
    } else {
      // After first close - show after 5 minutes from last close
      delay = Math.max(0, 300000 - (now - parseInt(lastClosedTime || '0')));
    }

    const timer = setTimeout(() => {
      setShowPopup(true);
    }, delay);

    // Cleanup timer on component unmount
    return () => clearTimeout(timer);
  }, [pathname]);

  const handleClose = () => {
    setShowPopup(false);

    // Get current close count and increment it
    const currentCloseCount = parseInt(localStorage.getItem('emailPopupCloseCount') || '0');
    const newCloseCount = currentCloseCount + 1;

    // Update close count and timestamp
    localStorage.setItem('emailPopupCloseCount', newCloseCount.toString());
    localStorage.setItem('emailPopupLastClosed', Date.now().toString());

    // If user has closed it twice, permanently dismiss
    if (newCloseCount >= 2) {
      localStorage.setItem('emailPopupPermanentlyDismissed', 'true');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('email', email);
      
      const response = await axios.post('/api/email', formData);
      
      if (response.data.success) {
        toast.success('Successfully subscribed to our newsletter!');
        setShowPopup(false);
        setEmail('');
        // Remember that user has subscribed (permanent dismissal)
        localStorage.setItem('emailSubscribed', 'true');
        // Clear any previous close tracking since they subscribed
        localStorage.removeItem('emailPopupCloseCount');
        localStorage.removeItem('emailPopupLastClosed');
        localStorage.removeItem('emailPopupPermanentlyDismissed');
      } else {
        toast.error('Subscription failed. Please try again.');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!showPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden">
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
          aria-label="Close popup"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Content */}
        <div className="p-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              SUBSCRIBE NOW
            </h2>
            <p className="text-gray-600 text-sm">
              DON'T MISS OUT ON THE LATEST BLOG POSTS<br />
              AND OFFERS.
            </p>
            <p className="text-xs text-gray-500 mt-2">
              Be the first to get notified.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
            </button>
          </form>

          <p className="text-xs text-gray-500 text-center mt-4">
            You can unsubscribe at any time. We respect your privacy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmailSubscriptionPopup;
