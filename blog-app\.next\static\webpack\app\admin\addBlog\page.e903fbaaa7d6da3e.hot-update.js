"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/addBlog/page",{

/***/ "(app-pages-browser)/./app/admin/addBlog/page.jsx":
/*!************************************!*\
  !*** ./app/admin/addBlog/page.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Components/AdminComponents/BlogTableItem */ \"(app-pages-browser)/./Components/AdminComponents/BlogTableItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BlogManagementPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"add\"); // 'add' or 'manage'\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"\",\n        commentsEnabled: true\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [tempBlogId, setTempBlogId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        fetchCategories();\n        fetchAuthors();\n        if (activeTab === \"manage\") {\n            fetchBlogs();\n        }\n    }, [\n        activeTab\n    ]);\n    // Generate a temporary blog ID for image uploads when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n    }, []);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n                setData((prev)=>({\n                        ...prev,\n                        category: response.data.categories[0].name\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No categories found. Please add categories in Settings.\");\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load categories\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n                setData((prev)=>({\n                        ...prev,\n                        author: response.data.authors[0].name,\n                        authorId: response.data.authors[0]._id,\n                        authorImg: response.data.authors[0].image || \"/author_img.png\"\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No authors found. Please add authors in Settings.\");\n                setAuthors([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load authors\");\n            setAuthors([]);\n        }\n    };\n    const fetchBlogs = async ()=>{\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load blogs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const onChangeHandler = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"authorId\") {\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData({\n                    ...data,\n                    author: selectedAuthor.name,\n                    authorId: selectedAuthor._id,\n                    authorImg: selectedAuthor.image || \"/author_img.png\"\n                });\n            }\n        } else {\n            setData({\n                ...data,\n                [name]: value\n            });\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        if (!image) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select a blog thumbnail image\");\n            return;\n        }\n        if (!data.title.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter a blog title\");\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"title\", data.title);\n        formData.append(\"description\", data.description);\n        formData.append(\"category\", data.category);\n        formData.append(\"author\", data.author);\n        formData.append(\"authorId\", data.authorId);\n        formData.append(\"authorImg\", data.authorImg);\n        formData.append(\"commentsEnabled\", data.commentsEnabled);\n        formData.append(\"image\", image);\n        formData.append(\"tempBlogId\", tempBlogId);\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog added successfully\");\n                setImage(false);\n                setData({\n                    title: \"\",\n                    description: \"\",\n                    category: categories.length > 0 ? categories[0].name : \"\",\n                    author: authors.length > 0 ? authors[0].name : \"\",\n                    authorId: authors.length > 0 ? authors[0]._id : \"\",\n                    authorImg: authors.length > 0 ? authors[0].image || \"/author_img.png\" : \"/author_img.png\"\n                });\n                // Reset temp blog ID and clear images\n                setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n                setAllImages([]);\n                // Switch to manage tab and refresh blog list\n                setActiveTab(\"manage\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.msg || \"Error adding blog\");\n            }\n        } catch (error) {\n            console.error(\"Error submitting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteBlog = async (mongoId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this blog?\")) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/blog\", {\n                params: {\n                    id: mongoId\n                }\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog deleted successfully\");\n            fetchBlogs();\n        } catch (error) {\n            console.error(\"Error deleting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    const insertBlogMention = (blogId, blogTitle)=>{\n        const mention = \"[[\".concat(blogId, \"|\").concat(blogTitle, \"]]\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        setShowBlogSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        if (!tempBlogId) return;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: tempBlogId,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", tempBlogId || \"new\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/images/\".concat(imageId));\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = \"{{image:\".concat(imageUrl, \"|\").concat(filename, \"}}\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Blog Management\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 326,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex border-b border-gray-300 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg \".concat(activeTab === \"add\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"add\"),\n                        children: \"Add New Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 330,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg ml-2 \".concat(activeTab === \"manage\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"manage\"),\n                        children: \"Manage Blogs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 329,\n                columnNumber: 13\n            }, undefined),\n            activeTab === \"add\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onSubmitHandler,\n                className: \"max-w-[800px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Create New Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 356,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Upload Thumbnail \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 86\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"image\",\n                                    className: \"cursor-pointer block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        className: \"border border-gray-300 rounded-md\",\n                                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                                        width: 200,\n                                        height: 120,\n                                        alt: \"\",\n                                        style: {\n                                            objectFit: \"cover\",\n                                            height: \"120px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    onChange: (e)=>setImage(e.target.files[0]),\n                                    type: \"file\",\n                                    id: \"image\",\n                                    hidden: true,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 358,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Blog Title \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    name: \"title\",\n                                    onChange: onChangeHandler,\n                                    value: data.title,\n                                    className: \"w-full px-4 py-3 border rounded-md\",\n                                    type: \"text\",\n                                    placeholder: \"Type here\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 373,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"blog-description\",\n                                        name: \"description\",\n                                        onChange: onChangeHandler,\n                                        value: data.description,\n                                        className: \"w-full px-4 py-3 border rounded-md\",\n                                        placeholder: \"Write content here\",\n                                        rows: 6,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center flex-wrap gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllBlogs();\n                                                setShowBlogSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Mention another blog\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllImages();\n                                                setShowImageSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Insert image\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Formats: [[blogId|blogTitle]] | \",\n                                                    \"{{image:url|filename}}\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 29\n                                }, undefined),\n                                showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Select a blog to mention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowBlogSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search blogs...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y\",\n                                                children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                        onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 relative mr-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: blog.image,\n                                                                    alt: blog.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: blog.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: blog.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, blog._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 33\n                                }, undefined),\n                                showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Insert Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowImageSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"Upload New Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: handleImageUpload,\n                                                                disabled: !selectedImageFile || imageUploadLoading,\n                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                                children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search images...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: imageSearchTerm,\n                                                onChange: (e)=>setImageSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                                children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative mb-2\",\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        src: image.url,\n                                                                        alt: image.filename,\n                                                                        fill: true,\n                                                                        className: \"object-cover rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            deleteImage(image._id, image.url);\n                                                                        },\n                                                                        className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        title: \"Delete image\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                                lineNumber: 561,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 65\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 truncate\",\n                                                                        children: image.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: new Date(image.uploadDate).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, image._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No images found. Upload your first image above.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 386,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading categories...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 33\n                                }, undefined) : categories.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    onChange: onChangeHandler,\n                                    value: data.category,\n                                    className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                    required: true,\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.name,\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No categories available. Please add categories in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 583,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading authors...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 33\n                                }, undefined) : authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"authorId\",\n                                            onChange: onChangeHandler,\n                                            value: data.authorId,\n                                            className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                            required: true,\n                                            children: authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: author._id,\n                                                    children: author.name\n                                                }, author._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 45\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: data.authorImg,\n                                                alt: data.author,\n                                                className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No authors available. Please add authors in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 606,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors\",\n                            disabled: loading || categories.length === 0 || authors.length === 0,\n                            children: loading ? \"Creating...\" : \"Create Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 641,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                    lineNumber: 355,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 354,\n                columnNumber: 17\n            }, undefined),\n            activeTab === \"manage\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[850px] bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Manage Blog Posts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 655,\n                        columnNumber: 21\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 659,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: \"Loading blogs...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 660,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 658,\n                        columnNumber: 25\n                    }, undefined) : blogs.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative overflow-x-auto border border-gray-300 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm text-gray-500 table-fixed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Author\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: blogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            mongoId: item._id,\n                                            title: item.title,\n                                            author: item.author,\n                                            authorImg: item.authorImg,\n                                            date: item.date,\n                                            deleteBlog: deleteBlog\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 664,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 663,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No blogs found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 698,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"add\"),\n                                className: \"mt-4 text-blue-600 hover:underline\",\n                                children: \"Add your first blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 699,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 697,\n                        columnNumber: 25\n                    }, undefined),\n                    blogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-sm text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Showing \",\n                                blogs.length,\n                                \" blog posts\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 711,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 710,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 654,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n        lineNumber: 325,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BlogManagementPage, \"wnAKy5iwsa+kpMyQuoXKgho6Us0=\");\n_c = BlogManagementPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogManagementPage);\nvar _c;\n$RefreshReg$(_c, \"BlogManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/addBlog/page.jsx\n"));

/***/ })

});