import mongoose from "mongoose";

const CommentReactionSchema = new mongoose.Schema({
  commentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Comment",
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true,
    index: true
  },
  reactionType: {
    type: String,
    required: true,
    enum: ['like', 'dislike'],
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound index to ensure a user can only have one reaction per comment
CommentReactionSchema.index({ commentId: 1, userId: 1 }, { unique: true });

const CommentReactionModel = mongoose.models.CommentReaction || mongoose.model('CommentReaction', CommentReactionSchema);

export default CommentReactionModel;
