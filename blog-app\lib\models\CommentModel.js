import mongoose from "mongoose";

const CommentSchema = new mongoose.Schema({
  blogId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "blog",
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true,
    index: true
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  parentCommentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Comment",
    default: null,
    index: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CommentSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for getting replies
CommentSchema.virtual('replies', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentCommentId'
});

// Virtual for getting like count
CommentSchema.virtual('likeCount', {
  ref: 'CommentReaction',
  localField: '_id',
  foreignField: 'commentId',
  count: true,
  match: { reactionType: 'like' }
});

// Virtual for getting dislike count
CommentSchema.virtual('dislikeCount', {
  ref: 'CommentReaction',
  localField: '_id',
  foreignField: 'commentId',
  count: true,
  match: { reactionType: 'dislike' }
});

// Ensure virtual fields are serialized
CommentSchema.set('toJSON', { virtuals: true });
CommentSchema.set('toObject', { virtuals: true });

const CommentModel = mongoose.models.Comment || mongoose.model('Comment', CommentSchema);

export default CommentModel;
