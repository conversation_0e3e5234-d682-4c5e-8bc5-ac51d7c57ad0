'use client'
import axios from 'axios'
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

const FeedbackPage = () => {
  const [feedbacks, setFeedbacks] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedFeedback, setSelectedFeedback] = useState(null)

  const fetchFeedbacks = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/feedback')
      if (response.data.success) {
        setFeedbacks(response.data.feedbacks)
      } else {
        toast.error('Failed to load feedbacks')
      }
    } catch (error) {
      console.error('Error fetching feedbacks:', error)
      toast.error('Failed to load feedbacks')
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (id) => {
    try {
      const response = await axios.put('/api/feedback', {
        id,
        isRead: true
      })
      
      if (response.data.success) {
        fetchFeedbacks()
        toast.success('Marked as read')
      }
    } catch (error) {
      toast.error('Failed to update feedback')
    }
  }

  const deleteFeedback = async (id) => {
    if (!window.confirm('Are you sure you want to delete this feedback?')) {
      return
    }
    
    try {
      const response = await axios.delete(`/api/feedback?id=${id}`)
      
      if (response.data.success) {
        fetchFeedbacks()
        if (selectedFeedback && selectedFeedback._id === id) {
          setSelectedFeedback(null)
        }
        toast.success('Feedback deleted')
      }
    } catch (error) {
      toast.error('Failed to delete feedback')
    }
  }

  useEffect(() => {
    fetchFeedbacks()
  }, [])

  return (
    <div className='flex-1 p-6'>
      <h1 className='text-2xl font-bold mb-6'>User Feedback</h1>
      
      <div className='flex flex-col md:flex-row gap-6'>
        <div className='w-full md:w-1/2'>
          <div className='bg-white rounded-lg shadow-md p-4'>
            <h2 className='text-lg font-semibold mb-4'>Feedback List</h2>
            
            {loading ? (
              <p className='text-center py-4'>Loading...</p>
            ) : feedbacks.length === 0 ? (
              <p className='text-center py-4'>No feedback submissions yet.</p>
            ) : (
              <div className='max-h-[600px] overflow-y-auto'>
                {feedbacks.map(feedback => (
                  <div 
                    key={feedback._id}
                    onClick={() => setSelectedFeedback(feedback)}
                    className={`p-3 border-b cursor-pointer hover:bg-gray-50 ${
                      !feedback.isRead ? 'bg-blue-50' : ''
                    } ${selectedFeedback?._id === feedback._id ? 'bg-gray-100' : ''}`}
                  >
                    <div className='flex justify-between items-center'>
                      <h3 className='font-medium'>{feedback.name}</h3>
                      <span className='text-xs text-gray-500'>
                        {new Date(feedback.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className='text-sm text-gray-600 truncate'>{feedback.message}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className='w-full md:w-1/2'>
          {selectedFeedback ? (
            <div className='bg-white rounded-lg shadow-md p-4'>
              <div className='flex justify-between items-center mb-4'>
                <h2 className='text-lg font-semibold'>Feedback Details</h2>
                <div className='flex gap-2'>
                  {!selectedFeedback.isRead && (
                    <button
                      onClick={() => markAsRead(selectedFeedback._id)}
                      className='px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600'
                    >
                      Mark as Read
                    </button>
                  )}
                  <button
                    onClick={() => deleteFeedback(selectedFeedback._id)}
                    className='px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600'
                  >
                    Delete
                  </button>
                </div>
              </div>
              
              <div className='space-y-4'>
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>From</h3>
                  <p>{selectedFeedback.name}</p>
                </div>
                
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Email</h3>
                  <p>{selectedFeedback.email}</p>
                </div>
                
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Date</h3>
                  <p>{new Date(selectedFeedback.createdAt).toLocaleString()}</p>
                </div>
                
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Message</h3>
                  <p className='whitespace-pre-wrap'>{selectedFeedback.message}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className='bg-white rounded-lg shadow-md p-4 flex items-center justify-center h-full'>
              <p className='text-gray-500'>Select a feedback to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default FeedbackPage