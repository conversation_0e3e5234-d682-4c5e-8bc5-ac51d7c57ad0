import Cookies from 'js-cookie'

// Set a cookie
export const setCookie = (name, value, options = {}) => {
  Cookies.set(name, value, options)
}

// Get a cookie
export const getCookie = (name) => {
  return Cookies.get(name)
}

// Remove a cookie
export const removeCookie = (name) => {
  Cookies.remove(name)
}

// Check if user has accepted cookies
export const hasAcceptedCookies = () => {
  return Cookies.get('cookie-consent') === 'accepted'
}

// Set analytics cookies (only if user has accepted)
export const setAnalyticsCookies = () => {
  if (hasAcceptedCookies()) {
    // Set analytics cookies here
    // Example: Cookies.set('_ga', 'GA1.2.123456789.1234567890', { expires: 365 })
  }
}

// Set functional cookies (only if user has accepted)
export const setFunctionalCookies = () => {
  if (hasAcceptedCookies()) {
    // Set functional cookies here
  }
}