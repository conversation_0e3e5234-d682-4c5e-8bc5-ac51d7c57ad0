import axios from 'axios';

// Track page view
export const trackPageView = async (path, contentType = 'page', blogId = null) => {
  try {
    // For testing purposes, let's track in both dev and prod
    // Remove this condition to only track in production
    // if (process.env.NODE_ENV === 'development') {
    //   console.log('Analytics tracking skipped in development mode');
    //   return;
    // }
    
    console.log('Tracking page view:', { path, contentType, blogId });
    
    // Get referrer
    const referrer = document.referrer || null;
    
    // Send analytics data
    const response = await axios.post('/api/analytics', {
      path,
      contentType,
      blogId,
      referrer
    });
    
    console.log('Analytics tracking response:', response.data);
  } catch (error) {
    // Silently fail - don't disrupt user experience for analytics
    console.error('Analytics error:', error);
  }
};

// Format numbers for display (e.g., 1000 -> 1K)
export const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// Fetch analytics data (for admin dashboard)
export const fetchAnalytics = async (period = '7days') => {
  const token = localStorage.getItem('authToken');
  
  if (!token) {
    throw new Error('Authentication required');
  }
  
  const response = await axios.get(`/api/analytics?period=${period}`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  
  return response.data;
};




