'use client'
import React, { useState } from 'react';

const CommentForm = ({ 
  onSubmit, 
  isLoggedIn, 
  onLoginRequired, 
  placeholder = "Write a comment...",
  buttonText = "Post Comment",
  maxLength = 1000,
  isReply = false,
  onCancel = null
}) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    if (!content.trim()) {
      return;
    }

    if (content.length > maxLength) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(content.trim());
      setContent(''); // Clear form on success
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setContent('');
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder={isLoggedIn ? placeholder : "Please log in to comment"}
          disabled={!isLoggedIn || isSubmitting}
          maxLength={maxLength}
          rows={isReply ? 3 : 4}
          className={`w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            !isLoggedIn ? 'bg-gray-100 cursor-not-allowed' : ''
          }`}
        />
        <div className="flex justify-between items-center mt-2">
          <span className={`text-sm ${
            content.length > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'
          }`}>
            {content.length}/{maxLength}
          </span>
          {content.length > maxLength && (
            <span className="text-sm text-red-500">
              Comment is too long
            </span>
          )}
        </div>
      </div>

      <div className="flex gap-3">
        {!isLoggedIn ? (
          <button
            type="button"
            onClick={onLoginRequired}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Log in to Comment
          </button>
        ) : (
          <>
            <button
              type="submit"
              disabled={!content.trim() || content.length > maxLength || isSubmitting}
              className={`px-4 py-2 rounded-lg transition-colors ${
                !content.trim() || content.length > maxLength || isSubmitting
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Posting...
                </div>
              ) : (
                buttonText
              )}
            </button>
            
            {isReply && onCancel && (
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            )}
          </>
        )}
      </div>
    </form>
  );
};

export default CommentForm;
