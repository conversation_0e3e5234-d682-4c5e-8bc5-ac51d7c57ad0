import mongoose from 'mongoose';

const AuthorSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Author name is required'],
    unique: true,
    trim: true
  },
  image: {
    type: String,
    default: '/author_img.png'
  },
  bio: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

export default mongoose.models.Author || mongoose.model('Author', AuthorSchema);