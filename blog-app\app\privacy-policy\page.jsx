'use client'
import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { assets } from '@/Assets/assets'
import Footer from '@/Components/Footer'

const PrivacyPolicyPage = () => {
  return (
    <>
      <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
        <div className='flex justify-between items-center'>
          <Link href='/'>
            <Image src={assets.logo} width={180} alt='Mr.Blogger' className='w-[130px] sm:w-auto' />
          </Link>
          <Link href='/'>
            <button className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'>
              Back to Home
            </button>
          </Link>
        </div>
        
        <div className='my-16 max-w-4xl mx-auto'>
          <h1 className='text-3xl sm:text-5xl font-semibold mb-8'>Privacy Policy & Cookies</h1>
          
          <div className='bg-white p-6 md:p-10 shadow-md'>
            <h2 className='text-2xl font-semibold mb-4'>Cookie Policy</h2>
            <p className='mb-4'>
              This website uses cookies to enhance your browsing experience. By using our website, 
              you agree to our use of cookies in accordance with this Cookie Policy.
            </p>
            
            <h3 className='text-xl font-semibold mt-6 mb-3'>What are cookies?</h3>
            <p className='mb-4'>
              Cookies are small text files that are placed on your device when you visit a website. 
              They are widely used to make websites work more efficiently and provide information to 
              the website owners.
            </p>
            
            <h3 className='text-xl font-semibold mt-6 mb-3'>How we use cookies</h3>
            <p className='mb-2'>We use cookies for the following purposes:</p>
            <ul className='list-disc ml-6 mb-4'>
              <li className='mb-2'>Essential cookies: These are necessary for the website to function properly.</li>
              <li className='mb-2'>Analytical/performance cookies: These allow us to recognize and count the number of visitors and see how visitors move around our website.</li>
              <li className='mb-2'>Functionality cookies: These enable the website to provide enhanced functionality and personalization.</li>
              <li className='mb-2'>Targeting cookies: These record your visit to our website, the pages you have visited, and the links you have followed.</li>
            </ul>
            
            <h3 className='text-xl font-semibold mt-6 mb-3'>Managing cookies</h3>
            <p className='mb-4'>
              You can set your browser to refuse all or some browser cookies, or to alert you when websites set or access cookies. 
              If you disable or refuse cookies, please note that some parts of this website may become inaccessible or not function properly.
            </p>
            
            <h3 className='text-xl font-semibold mt-6 mb-3'>Changes to our cookie policy</h3>
            <p className='mb-4'>
              We may update our Cookie Policy from time to time. Any changes we make to our Cookie Policy in the future will be posted on this page.
            </p>
            
            <h3 className='text-xl font-semibold mt-6 mb-3'>Contact us</h3>
            <p className='mb-4'>
              If you have any questions about our Cookie Policy, please contact us through our contact form.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}

export default PrivacyPolicyPage