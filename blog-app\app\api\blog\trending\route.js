import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import BlogModel from "@/lib/models/BlogModel";
import LikeModel from "@/lib/models/LikeModel";

export async function GET() {
  try {
    await ConnectDB();
    
    // Get all blogs
    const blogs = await BlogModel.find({}).lean();
    
    // Get like counts for each blog
    const blogPromises = blogs.map(async (blog) => {
      try {
        const count = await LikeModel.countDocuments({ blogId: blog._id });
        return {
          ...blog,
          likeCount: count
        };
      } catch (error) {
        console.error(`Error counting likes for blog ${blog._id}:`, error);
        return {
          ...blog,
          likeCount: 0
        };
      }
    });
    
    const blogsWithLikes = await Promise.all(blogPromises);
    
    // Sort blogs by like count (descending)
    const sortedBlogs = blogsWithLikes
      .sort((a, b) => b.likeCount - a.likeCount)
      .slice(0, 12); // Increase to 12 blogs
    
    return NextResponse.json({ 
      success: true, 
      blogs: sortedBlogs
    });
  } catch (error) {
    console.error("Error fetching trending blogs:", error);
    
    // Fallback to recent blogs if there's an error
    try {
      const recentBlogs = await BlogModel.find({})
        .sort({ createdAt: -1 })
        .limit(12) // Increase to 12 blogs
        .lean();
      
      return NextResponse.json({ 
        success: true, 
        blogs: recentBlogs
      });
    } catch (fallbackError) {
      console.error("Fallback error:", fallbackError);
      return NextResponse.json({ 
        success: false, 
        message: "Failed to fetch trending blogs" 
      }, { status: 500 });
    }
  }
}



