'use client'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    blogs: 0,
    users: 0,
    subscriptions: 0,
    loading: true
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [activityLoading, setActivityLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch blog count
        const blogsResponse = await axios.get('/api/blog');
        const blogs = blogsResponse.data.blogs || [];
        
        // Fetch user count
        const usersResponse = await axios.get('/api/users');
        const users = usersResponse.data.users || [];
        
        // Fetch subscription count
        const subscriptionsResponse = await axios.get('/api/email');
        const subscriptions = subscriptionsResponse.data.emails || [];
        
        setStats({
          blogs: blogs.length,
          users: users.length,
          subscriptions: subscriptions.length,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        toast.error('Failed to load dashboard statistics');
        setStats(prev => ({ ...prev, loading: false }));
      }
    };
    
    fetchStats();
  }, []);

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        setActivityLoading(true);
        const response = await axios.get('/api/activity');
        if (response.data.success) {
          setRecentActivity(response.data.activities || []);
        } else {
          toast.error('Failed to load recent activity');
        }
      } catch (error) {
        console.error('Error fetching recent activity:', error);
        toast.error('Failed to load recent activity');
      } finally {
        setActivityLoading(false);
      }
    };
    
    fetchRecentActivity();
  }, []);

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>Dashboard Overview</h1>
      
      {stats.loading ? (
        <div className='text-center py-10'>Loading dashboard data...</div>
      ) : (
        <>
          {/* Stats Cards */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
            <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md'>
              <h2 className='text-lg font-semibold text-gray-700'>Total Blog Posts</h2>
              <p className='text-3xl font-bold mt-2'>{stats.blogs}</p>
              <div className='mt-2'>
                <a href='/admin/addBlog?tab=manage' className='text-blue-600 text-sm hover:underline'>
                  View all posts →
                </a>
              </div>
            </div>
            
            <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md'>
              <h2 className='text-lg font-semibold text-gray-700'>Registered Users</h2>
              <p className='text-3xl font-bold mt-2'>{stats.users}</p>
              <div className='mt-2'>
                <a href='/admin/addUser' className='text-blue-600 text-sm hover:underline'>
                  Add new user →
                </a>
              </div>
            </div>
            
            <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md'>
              <h2 className='text-lg font-semibold text-gray-700'>Email Subscriptions</h2>
              <p className='text-3xl font-bold mt-2'>{stats.subscriptions}</p>
              <div className='mt-2'>
                <a href='/admin/subscriptions' className='text-blue-600 text-sm hover:underline'>
                  View all subscriptions →
                </a>
              </div>
            </div>
          </div>

          {/* Reactions Summary */}
          <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-lg font-semibold text-gray-700'>Blog Reactions</h2>
              <a href='/admin/reactions' className='text-blue-600 text-sm hover:underline'>
                View all reactions →
              </a>
            </div>
            
            {stats.loading ? (
              <p>Loading reaction data...</p>
            ) : (
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='border rounded-lg p-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      width="20" 
                      height="20" 
                      viewBox="0 0 24 24" 
                      fill="currentColor"
                      className="text-red-500"
                    >
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                    </svg>
                    <h3 className='font-medium'>Most Popular Posts</h3>
                  </div>
                  <a href='/admin/reactions?sort=most' className='text-blue-600 text-sm hover:underline'>
                    View most liked posts →
                  </a>
                </div>
                
                <div className='border rounded-lg p-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      width="20" 
                      height="20" 
                      viewBox="0 0 24 24" 
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-gray-500"
                    >
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                    </svg>
                    <h3 className='font-medium'>Least Popular Posts</h3>
                  </div>
                  <a href='/admin/reactions?sort=least' className='text-blue-600 text-sm hover:underline'>
                    View least liked posts →
                  </a>
                </div>
              </div>
            )}
          </div>
          
          {/* Quick Actions section removed */}
          
          {/* Recent Activity */}
          <div>
            <h2 className='text-xl font-semibold mb-4'>Recent Activity</h2>
            <div className='bg-white rounded-lg border border-gray-300 overflow-hidden'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Type
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Details
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Time
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {activityLoading ? (
                    <tr>
                      <td colSpan="3" className="px-6 py-4 text-center">
                        Loading recent activity...
                      </td>
                    </tr>
                  ) : recentActivity.length > 0 ? (
                    recentActivity.map((activity, index) => (
                      <tr key={index}>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <span className='font-medium'>{activity.type}</span>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          {activity.message}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                          {new Date(activity.timestamp).toLocaleString()}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="3" className="px-6 py-4 text-center">
                        No recent activity found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default AdminDashboard
