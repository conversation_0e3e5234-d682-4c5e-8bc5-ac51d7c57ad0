import mongoose from "mongoose";

export const ConnectDB = async () => {
    try {
        // Check if already connected
        if (mongoose.connections[0].readyState) {
            console.log("DB Already Connected");
            return;
        }

        const connectionString = process.env.MONGODB_URI || 'mongodb+srv://subhashanas:<EMAIL>/blog-app';

        await mongoose.connect(connectionString, {
            serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
            socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
        });

        console.log("DB Connected");
    } catch (error) {
        console.error("DB Connection Error:", error.message);
        // Don't throw the error to prevent 500 responses
        // The API will handle the case where DB is not connected
    }
}