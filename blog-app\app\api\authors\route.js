import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import Author from '@/models/Author';

// Connect to database
export async function GET() {
  try {
    await ConnectDB();
    const authors = await Author.find({}).sort({ name: 1 });
    
    return NextResponse.json({
      success: true,
      authors
    });
  } catch (error) {
    console.error('Error fetching authors:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch authors'
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    await ConnectDB();
    
    const formData = await request.formData();
    const name = formData.get('name');
    const bio = formData.get('bio');
    const imageFile = formData.get('image');
    
    if (!name) {
      return NextResponse.json({
        success: false,
        message: 'Author name is required'
      }, { status: 400 });
    }
    
    // Check if author already exists
    const existingAuthor = await Author.findOne({ name });
    if (existingAuthor) {
      return NextResponse.json({
        success: false,
        message: 'Author already exists'
      }, { status: 409 });
    }
    
    // Create author data object
    const authorData = {
      name,
      bio: bio || ''
    };
    
    // Upload image if provided
    if (imageFile && imageFile.size > 0) {
      const imageByteData = await imageFile.arrayBuffer();
      const buffer = Buffer.from(imageByteData);
      const timestamp = Date.now();
      const path = `./public/authors/${timestamp}_${imageFile.name}`;
      
      // Ensure directory exists
      const fs = require('fs');
      if (!fs.existsSync('./public/authors')) {
        fs.mkdirSync('./public/authors', { recursive: true });
      }
      
      // Write file
      const { writeFile } = require('fs/promises');
      await writeFile(path, buffer);
      
      // Set image path
      authorData.image = `/authors/${timestamp}_${imageFile.name}`;
    }
    
    // Create new author
    const newAuthor = new Author(authorData);
    await newAuthor.save();
    
    return NextResponse.json({
      success: true,
      message: 'Author added successfully',
      author: newAuthor
    });
  } catch (error) {
    console.error('Error creating author:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to create author'
    }, { status: 500 });
  }
}

export async function DELETE(request) {
  try {
    await ConnectDB();
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        success: false,
        message: 'Author ID is required'
      }, { status: 400 });
    }
    
    const deletedAuthor = await Author.findByIdAndDelete(id);
    
    if (!deletedAuthor) {
      return NextResponse.json({
        success: false,
        message: 'Author not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Author deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting author:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to delete author'
    }, { status: 500 });
  }
}

