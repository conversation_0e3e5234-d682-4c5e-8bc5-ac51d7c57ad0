'use client'
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const DebugPage = () => {
  const [tokenInfo, setTokenInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkToken = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('authToken');
        
        if (!token) {
          setError("No token found in localStorage");
          setLoading(false);
          return;
        }
        
        console.log("Token from localStorage:", token);
        
        const response = await axios.get('/api/debug', {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        setTokenInfo(response.data);
      } catch (error) {
        console.error("Token check error:", error);
        setError(error.response?.data?.message || "Failed to verify token");
      } finally {
        setLoading(false);
      }
    };
    
    checkToken();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Token Debug Page</h1>
      
      {loading ? (
        <div>Checking token...</div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{error}</p>
        </div>
      ) : tokenInfo ? (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <p className="font-bold">Token is valid</p>
          <pre className="mt-2 bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(tokenInfo, null, 2)}
          </pre>
        </div>
      ) : (
        <div>No token information available</div>
      )}
      
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">Manual Token Check</h2>
        <button 
          onClick={async () => {
            const token = localStorage.getItem('authToken');
            console.log("Current token:", token);
            
            try {
              const response = await axios.get('/api/debug', {
                headers: { Authorization: `Bearer ${token}` }
              });
              console.log("Token check result:", response.data);
              alert("Token is valid. Check console for details.");
            } catch (error) {
              console.error("Token check failed:", error);
              alert("Token check failed. Check console for details.");
            }
          }}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Check Current Token
        </button>
      </div>
    </div>
  );
};

export default DebugPage;