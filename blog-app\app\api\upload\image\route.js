import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';
import { ConnectDB } from "@/lib/config/db";
import ImageModel from "@/lib/models/ImageModel";

export async function POST(request) {
  try {
    // Connect to database
    await ConnectDB();
    
    const formData = await request.formData();
    const image = formData.get('image');
    const blogId = formData.get('blogId') || null;
    
    if (!image) {
      return NextResponse.json({ 
        success: false, 
        message: "No image provided" 
      }, { status: 400 });
    }

    // Get image data as array buffer
    const imageByteData = await image.arrayBuffer();
    const buffer = Buffer.from(imageByteData);
    
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const filename = `${timestamp}_${image.name.replace(/\s+/g, '_')}`;
    
    // Ensure directory exists
    const fs = require('fs');
    if (!fs.existsSync('./public/blog-images')) {
      fs.mkdirSync('./public/blog-images', { recursive: true });
    }
    
    const filepath = `./public/blog-images/${filename}`;
    
    // Write file to public directory
    await writeFile(filepath, buffer);
    
    // Return the URL path to the image
    const imageUrl = `/blog-images/${filename}`;
    
    // Save image to database
    const imageDoc = await ImageModel.create({
      filename: image.name,
      path: filepath,
      url: imageUrl,
      contentType: image.type,
      size: buffer.length,
      data: buffer,
      blogId: blogId === 'new' ? null : blogId
    });
    
    return NextResponse.json({ 
      success: true, 
      imageUrl: imageUrl,
      imageId: imageDoc._id
    });
    
  } catch (error) {
    console.error("Error uploading image:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to upload image" 
    }, { status: 500 });
  }
}




