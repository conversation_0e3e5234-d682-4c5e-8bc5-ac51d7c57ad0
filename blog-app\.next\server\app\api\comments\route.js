"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/comments/route";
exports.ids = ["app/api/comments/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Froute&page=%2Fapi%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Froute&page=%2Fapi%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/comments/route.js */ \"(rsc)/./app/api/comments/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/comments/route\",\n        pathname: \"/api/comments\",\n        filename: \"route\",\n        bundlePath: \"app/api/comments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\comments\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/comments/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Froute&page=%2Fapi%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/comments/route.js":
/*!***********************************!*\
  !*** ./app/api/comments/route.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n/* harmony import */ var _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CommentModel */ \"(rsc)/./lib/models/CommentModel.js\");\n/* harmony import */ var _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/models/CommentReactionModel */ \"(rsc)/./lib/models/CommentReactionModel.js\");\n/* harmony import */ var _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/models/CommentSettingsModel */ \"(rsc)/./lib/models/CommentSettingsModel.js\");\n/* harmony import */ var _lib_models_UserModel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/models/UserModel */ \"(rsc)/./lib/models/UserModel.js\");\n\n\n\n\n\n\n\n// Helper function to get user from token\nfunction getUserFromToken(request) {\n    const authHeader = request.headers.get(\"Authorization\");\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    const token = authHeader.split(\" \")[1];\n    return (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n}\n// Helper function to check if comments are enabled\nasync function checkCommentsEnabled(blogId = null) {\n    try {\n        // Check blog-specific settings first, then global settings\n        let settings = null;\n        if (blogId) {\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                blogId\n            });\n        }\n        if (!settings) {\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                blogId: null\n            });\n        }\n        return settings ? settings.commentsEnabled : true; // Default to enabled\n    } catch (error) {\n        console.error(\"Error checking comment settings:\", error);\n        return true; // Default to enabled on error\n    }\n}\n// GET - Fetch comments for a blog\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const { searchParams } = new URL(request.url);\n        const blogId = searchParams.get(\"blogId\");\n        const page = parseInt(searchParams.get(\"page\")) || 1;\n        const limit = parseInt(searchParams.get(\"limit\")) || 10;\n        if (!blogId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Blog ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if comments are enabled\n        const commentsEnabled = await checkCommentsEnabled(blogId);\n        if (!commentsEnabled) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comments are disabled for this blog\"\n            }, {\n                status: 403\n            });\n        }\n        const skip = (page - 1) * limit;\n        // Fetch top-level comments (no parent) with user info\n        const comments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n            blogId,\n            parentCommentId: null,\n            isDeleted: false\n        }).populate(\"userId\", \"name email profilePicture\").sort({\n            createdAt: -1\n        }).skip(skip).limit(limit);\n        // For each comment, fetch its replies and reaction counts\n        const commentsWithDetails = await Promise.all(comments.map(async (comment)=>{\n            // Fetch replies\n            const replies = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n                parentCommentId: comment._id,\n                isDeleted: false\n            }).populate(\"userId\", \"name email profilePicture\").sort({\n                createdAt: 1\n            });\n            // Fetch reaction counts\n            const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId: comment._id,\n                reactionType: \"like\"\n            });\n            const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId: comment._id,\n                reactionType: \"dislike\"\n            });\n            // Add reaction counts to replies as well\n            const repliesWithReactions = await Promise.all(replies.map(async (reply)=>{\n                const replyLikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId: reply._id,\n                    reactionType: \"like\"\n                });\n                const replyDislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                    commentId: reply._id,\n                    reactionType: \"dislike\"\n                });\n                return {\n                    ...reply.toObject(),\n                    likeCount: replyLikeCount,\n                    dislikeCount: replyDislikeCount\n                };\n            }));\n            return {\n                ...comment.toObject(),\n                replies: repliesWithReactions,\n                likeCount,\n                dislikeCount\n            };\n        }));\n        // Get total count for pagination\n        const totalComments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments({\n            blogId,\n            parentCommentId: null,\n            isDeleted: false\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            comments: commentsWithDetails,\n            pagination: {\n                page,\n                limit,\n                total: totalComments,\n                pages: Math.ceil(totalComments / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching comments:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch comments\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create a new comment\nasync function POST(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { blogId, content, parentCommentId = null } = body;\n        if (!blogId || !content) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Blog ID and content are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if comments are enabled\n        const commentsEnabled = await checkCommentsEnabled(blogId);\n        if (!commentsEnabled) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comments are disabled for this blog\"\n            }, {\n                status: 403\n            });\n        }\n        // Validate content length\n        if (content.trim().length === 0 || content.length > 1000) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comment must be between 1 and 1000 characters\"\n            }, {\n                status: 400\n            });\n        }\n        // If this is a reply, verify the parent comment exists\n        if (parentCommentId) {\n            const parentComment = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(parentCommentId);\n            if (!parentComment || parentComment.isDeleted) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    message: \"Parent comment not found\"\n                }, {\n                    status: 404\n                });\n            }\n        }\n        // Create the comment\n        const newComment = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n            blogId,\n            userId: userData.userId,\n            content: content.trim(),\n            parentCommentId\n        });\n        // Populate user info for response\n        await newComment.populate(\"userId\", \"name email profilePicture\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Comment created successfully\",\n            comment: {\n                ...newComment.toObject(),\n                likeCount: 0,\n                dislikeCount: 0,\n                replies: []\n            }\n        });\n    } catch (error) {\n        console.error(\"Error creating comment:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to create comment\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/comments/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentModel.js":
/*!************************************!*\
  !*** ./lib/models/CommentModel.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    content: {\n        type: String,\n        required: true,\n        trim: true,\n        maxlength: 1000\n    },\n    parentCommentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        default: null,\n        index: true\n    },\n    isDeleted: {\n        type: Boolean,\n        default: false\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Update the updatedAt field before saving\nCommentSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Virtual for getting replies\nCommentSchema.virtual(\"replies\", {\n    ref: \"Comment\",\n    localField: \"_id\",\n    foreignField: \"parentCommentId\"\n});\n// Virtual for getting like count\nCommentSchema.virtual(\"likeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"like\"\n    }\n});\n// Virtual for getting dislike count\nCommentSchema.virtual(\"dislikeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"dislike\"\n    }\n});\n// Ensure virtual fields are serialized\nCommentSchema.set(\"toJSON\", {\n    virtuals: true\n});\nCommentSchema.set(\"toObject\", {\n    virtuals: true\n});\nconst CommentModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Comment || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Comment\", CommentSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0NvbW1lbnRNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsZ0JBQWdCLElBQUlELHdEQUFlLENBQUM7SUFDeENHLFFBQVE7UUFDTkMsTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FDLFFBQVE7UUFDTk4sTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FFLFNBQVM7UUFDUFAsTUFBTVE7UUFDTkosVUFBVTtRQUNWSyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBQyxpQkFBaUI7UUFDZlgsTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xTLFNBQVM7UUFDVFAsT0FBTztJQUNUO0lBQ0FRLFdBQVc7UUFDVGIsTUFBTWM7UUFDTkYsU0FBUztJQUNYO0lBQ0FHLFdBQVc7UUFDVGYsTUFBTWdCO1FBQ05KLFNBQVNJLEtBQUtDLEdBQUc7UUFDakJaLE9BQU87SUFDVDtJQUNBYSxXQUFXO1FBQ1RsQixNQUFNZ0I7UUFDTkosU0FBU0ksS0FBS0MsR0FBRztJQUNuQjtBQUNGO0FBRUEsMkNBQTJDO0FBQzNDcEIsY0FBY3NCLEdBQUcsQ0FBQyxRQUFRLFNBQVNDLElBQUk7SUFDckMsSUFBSSxDQUFDRixTQUFTLEdBQUdGLEtBQUtDLEdBQUc7SUFDekJHO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDOUJ2QixjQUFjd0IsT0FBTyxDQUFDLFdBQVc7SUFDL0JsQixLQUFLO0lBQ0xtQixZQUFZO0lBQ1pDLGNBQWM7QUFDaEI7QUFFQSxpQ0FBaUM7QUFDakMxQixjQUFjd0IsT0FBTyxDQUFDLGFBQWE7SUFDakNsQixLQUFLO0lBQ0xtQixZQUFZO0lBQ1pDLGNBQWM7SUFDZEMsT0FBTztJQUNQQyxPQUFPO1FBQUVDLGNBQWM7SUFBTztBQUNoQztBQUVBLG9DQUFvQztBQUNwQzdCLGNBQWN3QixPQUFPLENBQUMsZ0JBQWdCO0lBQ3BDbEIsS0FBSztJQUNMbUIsWUFBWTtJQUNaQyxjQUFjO0lBQ2RDLE9BQU87SUFDUEMsT0FBTztRQUFFQyxjQUFjO0lBQVU7QUFDbkM7QUFFQSx1Q0FBdUM7QUFDdkM3QixjQUFjOEIsR0FBRyxDQUFDLFVBQVU7SUFBRUMsVUFBVTtBQUFLO0FBQzdDL0IsY0FBYzhCLEdBQUcsQ0FBQyxZQUFZO0lBQUVDLFVBQVU7QUFBSztBQUUvQyxNQUFNQyxlQUFlakMsd0RBQWUsQ0FBQ21DLE9BQU8sSUFBSW5DLHFEQUFjLENBQUMsV0FBV0M7QUFFMUUsaUVBQWVnQyxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQ29tbWVudE1vZGVsLmpzPzMxNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBDb21tZW50U2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIGJsb2dJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwiYmxvZ1wiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIHVzZXJJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwidXNlclwiLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIGNvbnRlbnQ6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IDEwMDBcbiAgfSxcbiAgcGFyZW50Q29tbWVudElkOiB7XG4gICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgIHJlZjogXCJDb21tZW50XCIsXG4gICAgZGVmYXVsdDogbnVsbCxcbiAgICBpbmRleDogdHJ1ZVxuICB9LFxuICBpc0RlbGV0ZWQ6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IGZhbHNlXG4gIH0sXG4gIGNyZWF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3csXG4gICAgaW5kZXg6IHRydWVcbiAgfSxcbiAgdXBkYXRlZEF0OiB7XG4gICAgdHlwZTogRGF0ZSxcbiAgICBkZWZhdWx0OiBEYXRlLm5vd1xuICB9XG59KTtcblxuLy8gVXBkYXRlIHRoZSB1cGRhdGVkQXQgZmllbGQgYmVmb3JlIHNhdmluZ1xuQ29tbWVudFNjaGVtYS5wcmUoJ3NhdmUnLCBmdW5jdGlvbihuZXh0KSB7XG4gIHRoaXMudXBkYXRlZEF0ID0gRGF0ZS5ub3coKTtcbiAgbmV4dCgpO1xufSk7XG5cbi8vIFZpcnR1YWwgZm9yIGdldHRpbmcgcmVwbGllc1xuQ29tbWVudFNjaGVtYS52aXJ0dWFsKCdyZXBsaWVzJywge1xuICByZWY6ICdDb21tZW50JyxcbiAgbG9jYWxGaWVsZDogJ19pZCcsXG4gIGZvcmVpZ25GaWVsZDogJ3BhcmVudENvbW1lbnRJZCdcbn0pO1xuXG4vLyBWaXJ0dWFsIGZvciBnZXR0aW5nIGxpa2UgY291bnRcbkNvbW1lbnRTY2hlbWEudmlydHVhbCgnbGlrZUNvdW50Jywge1xuICByZWY6ICdDb21tZW50UmVhY3Rpb24nLFxuICBsb2NhbEZpZWxkOiAnX2lkJyxcbiAgZm9yZWlnbkZpZWxkOiAnY29tbWVudElkJyxcbiAgY291bnQ6IHRydWUsXG4gIG1hdGNoOiB7IHJlYWN0aW9uVHlwZTogJ2xpa2UnIH1cbn0pO1xuXG4vLyBWaXJ0dWFsIGZvciBnZXR0aW5nIGRpc2xpa2UgY291bnRcbkNvbW1lbnRTY2hlbWEudmlydHVhbCgnZGlzbGlrZUNvdW50Jywge1xuICByZWY6ICdDb21tZW50UmVhY3Rpb24nLFxuICBsb2NhbEZpZWxkOiAnX2lkJyxcbiAgZm9yZWlnbkZpZWxkOiAnY29tbWVudElkJyxcbiAgY291bnQ6IHRydWUsXG4gIG1hdGNoOiB7IHJlYWN0aW9uVHlwZTogJ2Rpc2xpa2UnIH1cbn0pO1xuXG4vLyBFbnN1cmUgdmlydHVhbCBmaWVsZHMgYXJlIHNlcmlhbGl6ZWRcbkNvbW1lbnRTY2hlbWEuc2V0KCd0b0pTT04nLCB7IHZpcnR1YWxzOiB0cnVlIH0pO1xuQ29tbWVudFNjaGVtYS5zZXQoJ3RvT2JqZWN0JywgeyB2aXJ0dWFsczogdHJ1ZSB9KTtcblxuY29uc3QgQ29tbWVudE1vZGVsID0gbW9uZ29vc2UubW9kZWxzLkNvbW1lbnQgfHwgbW9uZ29vc2UubW9kZWwoJ0NvbW1lbnQnLCBDb21tZW50U2NoZW1hKTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tbWVudE1vZGVsO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiQ29tbWVudFNjaGVtYSIsIlNjaGVtYSIsImJsb2dJZCIsInR5cGUiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwicmVxdWlyZWQiLCJpbmRleCIsInVzZXJJZCIsImNvbnRlbnQiLCJTdHJpbmciLCJ0cmltIiwibWF4bGVuZ3RoIiwicGFyZW50Q29tbWVudElkIiwiZGVmYXVsdCIsImlzRGVsZXRlZCIsIkJvb2xlYW4iLCJjcmVhdGVkQXQiLCJEYXRlIiwibm93IiwidXBkYXRlZEF0IiwicHJlIiwibmV4dCIsInZpcnR1YWwiLCJsb2NhbEZpZWxkIiwiZm9yZWlnbkZpZWxkIiwiY291bnQiLCJtYXRjaCIsInJlYWN0aW9uVHlwZSIsInNldCIsInZpcnR1YWxzIiwiQ29tbWVudE1vZGVsIiwibW9kZWxzIiwiQ29tbWVudCIsIm1vZGVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentReactionModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentReactionModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentReactionSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    commentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    reactionType: {\n        type: String,\n        required: true,\n        enum: [\n            \"like\",\n            \"dislike\"\n        ],\n        index: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Create compound index to ensure a user can only have one reaction per comment\nCommentReactionSchema.index({\n    commentId: 1,\n    userId: 1\n}, {\n    unique: true\n});\nconst CommentReactionModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentReaction || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentReaction\", CommentReactionSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentReactionModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentReactionModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentSettingsModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentSettingsModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSettingsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Global settings\n    commentsEnabled: {\n        type: Boolean,\n        default: true\n    },\n    requireLogin: {\n        type: Boolean,\n        default: true\n    },\n    allowReplies: {\n        type: Boolean,\n        default: true\n    },\n    allowReactions: {\n        type: Boolean,\n        default: true\n    },\n    maxCommentLength: {\n        type: Number,\n        default: 1000,\n        min: 100,\n        max: 5000\n    },\n    moderationEnabled: {\n        type: Boolean,\n        default: false\n    },\n    // Per-blog settings (optional)\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        default: null,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true\n    }\n});\n// Update the updatedAt field before saving\nCommentSettingsSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Ensure only one global setting (where blogId is null)\nCommentSettingsSchema.index({\n    blogId: 1\n}, {\n    unique: true,\n    sparse: true\n});\nconst CommentSettingsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentSettings || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentSettings\", CommentSettingsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentSettingsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentSettingsModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/UserModel.js":
/*!*********************************!*\
  !*** ./lib/models/UserModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    password: {\n        type: String,\n        required: true\n    },\n    role: {\n        type: String,\n        default: \"user\",\n        enum: [\n            \"user\",\n            \"admin\"\n        ]\n    },\n    profilePicture: {\n        type: String,\n        default: \"/default_profile.png\"\n    },\n    name: {\n        type: String,\n        default: \"\"\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst UserModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).user || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"user\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL1VzZXJNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFPO1FBQ0hDLE1BQU1DO1FBQ05DLFVBQVU7UUFDVkMsUUFBUTtJQUNaO0lBQ0FDLFVBQVU7UUFDTkosTUFBTUM7UUFDTkMsVUFBVTtJQUNkO0lBQ0FHLE1BQU07UUFDRkwsTUFBTUM7UUFDTkssU0FBUztRQUNUQyxNQUFNO1lBQUM7WUFBUTtTQUFRO0lBQzNCO0lBQ0FDLGdCQUFnQjtRQUNaUixNQUFNQztRQUNOSyxTQUFTO0lBQ2I7SUFDQUcsTUFBTTtRQUNGVCxNQUFNQztRQUNOSyxTQUFTO0lBQ2I7SUFDQUksTUFBTTtRQUNGVixNQUFNVztRQUNOTCxTQUFTSyxLQUFLQyxHQUFHO0lBQ3JCO0FBQ0o7QUFFQSxNQUFNQyxZQUFZaEIsd0RBQWUsQ0FBQ2tCLElBQUksSUFBSWxCLHFEQUFjLENBQUMsUUFBUUM7QUFFakUsaUVBQWVlLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vbGliL21vZGVscy9Vc2VyTW9kZWwuanM/N2VkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSBcIm1vbmdvb3NlXCI7XHJcblxyXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcclxuICAgIGVtYWlsOiB7XHJcbiAgICAgICAgdHlwZTogU3RyaW5nLFxyXG4gICAgICAgIHJlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIHVuaXF1ZTogdHJ1ZVxyXG4gICAgfSxcclxuICAgIHBhc3N3b3JkOiB7XHJcbiAgICAgICAgdHlwZTogU3RyaW5nLFxyXG4gICAgICAgIHJlcXVpcmVkOiB0cnVlXHJcbiAgICB9LFxyXG4gICAgcm9sZToge1xyXG4gICAgICAgIHR5cGU6IFN0cmluZyxcclxuICAgICAgICBkZWZhdWx0OiAndXNlcicsXHJcbiAgICAgICAgZW51bTogWyd1c2VyJywgJ2FkbWluJ11cclxuICAgIH0sXHJcbiAgICBwcm9maWxlUGljdHVyZToge1xyXG4gICAgICAgIHR5cGU6IFN0cmluZyxcclxuICAgICAgICBkZWZhdWx0OiAnL2RlZmF1bHRfcHJvZmlsZS5wbmcnXHJcbiAgICB9LFxyXG4gICAgbmFtZToge1xyXG4gICAgICAgIHR5cGU6IFN0cmluZyxcclxuICAgICAgICBkZWZhdWx0OiAnJ1xyXG4gICAgfSxcclxuICAgIGRhdGU6IHtcclxuICAgICAgICB0eXBlOiBEYXRlLFxyXG4gICAgICAgIGRlZmF1bHQ6IERhdGUubm93KClcclxuICAgIH1cclxufSk7XHJcblxyXG5jb25zdCBVc2VyTW9kZWwgPSBtb25nb29zZS5tb2RlbHMudXNlciB8fCBtb25nb29zZS5tb2RlbCgndXNlcicsIFNjaGVtYSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBVc2VyTW9kZWw7XHJcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIlNjaGVtYSIsImVtYWlsIiwidHlwZSIsIlN0cmluZyIsInJlcXVpcmVkIiwidW5pcXVlIiwicGFzc3dvcmQiLCJyb2xlIiwiZGVmYXVsdCIsImVudW0iLCJwcm9maWxlUGljdHVyZSIsIm5hbWUiLCJkYXRlIiwiRGF0ZSIsIm5vdyIsIlVzZXJNb2RlbCIsIm1vZGVscyIsInVzZXIiLCJtb2RlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/UserModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Froute&page=%2Fapi%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();