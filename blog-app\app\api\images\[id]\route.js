import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import ImageModel from "@/lib/models/ImageModel";
import fs from 'fs';
import path from 'path';

export async function GET(request, { params }) {
  try {
    await ConnectDB();
    
    const imageId = params.id;
    const image = await ImageModel.findById(imageId);
    
    if (!image) {
      return NextResponse.json({ 
        success: false, 
        message: "Image not found" 
      }, { status: 404 });
    }
    
    // Create response with appropriate content type
    const response = new NextResponse(image.data);
    response.headers.set('Content-Type', image.contentType);
    response.headers.set('Content-Length', image.size.toString());
    response.headers.set('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
    
    return response;
    
  } catch (error) {
    console.error("Error retrieving image:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to retrieve image" 
    }, { status: 500 });
  }
}

export async function DELETE(request, { params }) {
  try {
    await ConnectDB();

    const imageId = params.id;
    const image = await ImageModel.findById(imageId);

    if (!image) {
      return NextResponse.json({
        success: false,
        message: "Image not found"
      }, { status: 404 });
    }

    // Delete the physical file if it exists
    if (image.path) {
      const filePath = path.join(process.cwd(), 'public', image.path);
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      } catch (fileError) {
        console.error("Error deleting physical file:", fileError);
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete from database
    await ImageModel.findByIdAndDelete(imageId);

    return NextResponse.json({
      success: true,
      message: "Image deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting image:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to delete image"
    }, { status: 500 });
  }
}