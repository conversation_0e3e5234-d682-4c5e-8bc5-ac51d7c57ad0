import mongoose from "mongoose";

const CommentSettingsSchema = new mongoose.Schema({
  // Global settings
  commentsEnabled: {
    type: Boolean,
    default: true
  },
  requireLogin: {
    type: Boolean,
    default: true
  },
  allowReplies: {
    type: Boolean,
    default: true
  },
  allowReactions: {
    type: Boolean,
    default: true
  },
  maxCommentLength: {
    type: Number,
    default: 1000,
    min: 100,
    max: 5000
  },
  moderationEnabled: {
    type: Boolean,
    default: false
  },
  // Per-blog settings (optional)
  blogId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "blog",
    default: null,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true
  }
});

// Update the updatedAt field before saving
CommentSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Ensure only one global setting (where blogId is null)
CommentSettingsSchema.index({ blogId: 1 }, { unique: true, sparse: true });

const CommentSettingsModel = mongoose.models.CommentSettings || mongoose.model('CommentSettings', CommentSettingsSchema);

export default CommentSettingsModel;
