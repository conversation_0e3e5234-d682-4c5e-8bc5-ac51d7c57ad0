"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog/route";
exports.ids = ["app/api/blog/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/blog/route.js */ \"(rsc)/./app/api/blog/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog/route\",\n        pathname: \"/api/blog\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\blog\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/blog/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/blog/route.js":
/*!*******************************!*\
  !*** ./app/api/blog/route.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/models/BlogModel */ \"(rsc)/./lib/models/BlogModel.js\");\n/* harmony import */ var _lib_models_ImageModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/ImageModel */ \"(rsc)/./lib/models/ImageModel.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\nconst { NextResponse } = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/server.js\");\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst LoadDB = async ()=>{\n    await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n};\nLoadDB();\n// API Endpoint to get all blogs\nasync function GET(request) {\n    try {\n        const blogId = request.nextUrl.searchParams.get(\"id\");\n        if (blogId) {\n            const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(blogId);\n            return NextResponse.json(blog);\n        } else {\n            const blogs = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({});\n            return NextResponse.json({\n                blogs\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in GET /api/blog:\", error.message);\n        return NextResponse.json({\n            success: false,\n            message: \"Database connection error. Please try again later.\",\n            blogs: []\n        }, {\n            status: 503\n        });\n    }\n}\n// API Endpoint For Uploading Blogs\nasync function POST(request) {\n    const formData = await request.formData();\n    const timestamp = Date.now();\n    const image = formData.get(\"image\");\n    const imageByteData = await image.arrayBuffer();\n    const buffer = Buffer.from(imageByteData);\n    const path = `./public/${timestamp}_${image.name}`;\n    await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(path, buffer);\n    const imgUrl = `/${timestamp}_${image.name}`;\n    const blogData = {\n        title: `${formData.get(\"title\")}`,\n        description: `${formData.get(\"description\")}`,\n        category: `${formData.get(\"category\")}`,\n        author: `${formData.get(\"author\")}`,\n        image: `${imgUrl}`,\n        authorImg: `${formData.get(\"authorImg\")}`\n    };\n    const newBlog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create(blogData);\n    console.log(\"Blog Saved\");\n    // Transfer temporary images to the new blog\n    const tempBlogId = formData.get(\"tempBlogId\");\n    if (tempBlogId) {\n        try {\n            await _lib_models_ImageModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].updateMany({\n                blogId: tempBlogId\n            }, {\n                blogId: newBlog._id.toString()\n            });\n            console.log(`Transferred images from ${tempBlogId} to ${newBlog._id}`);\n        } catch (error) {\n            console.error(\"Error transferring images:\", error);\n        }\n    }\n    return NextResponse.json({\n        success: true,\n        msg: \"Blog Added\"\n    });\n}\n// Creating API Endpoint to delete Blog\nasync function DELETE(request) {\n    try {\n        const id = await request.nextUrl.searchParams.get(\"id\");\n        const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(id);\n        if (!blog) {\n            return NextResponse.json({\n                success: false,\n                msg: \"Blog not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Delete the image file if it exists\n        if (blog.image) {\n            fs.unlink(`./public${blog.image}`, ()=>{});\n        }\n        await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndDelete(id);\n        return NextResponse.json({\n            success: true,\n            msg: \"Blog Deleted\"\n        });\n    } catch (error) {\n        console.error(\"Error in DELETE /api/blog:\", error.message);\n        return NextResponse.json({\n            success: false,\n            msg: \"Database connection error. Please try again later.\"\n        }, {\n            status: 503\n        });\n    }\n}\n// API Endpoint For Updating Blogs\nasync function PUT(request) {\n    try {\n        const formData = await request.formData();\n        const blogId = formData.get(\"id\");\n        // Find the blog to update\n        const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(blogId);\n        if (!blog) {\n            return NextResponse.json({\n                success: false,\n                msg: \"Blog not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Update blog data\n        blog.title = formData.get(\"title\");\n        blog.description = formData.get(\"description\");\n        blog.category = formData.get(\"category\");\n        blog.author = formData.get(\"author\");\n        blog.authorImg = formData.get(\"authorImg\");\n        // Check if a new image was uploaded\n        const image = formData.get(\"image\");\n        if (image && image.name) {\n            // Process new image\n            const imageByteData = await image.arrayBuffer();\n            const buffer = Buffer.from(imageByteData);\n            const timestamp = Date.now();\n            const path = `./public/${timestamp}_${image.name}`;\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(path, buffer);\n            const imgUrl = `/${timestamp}_${image.name}`;\n            // Delete old image if it exists\n            if (blog.image) {\n                try {\n                    fs.unlink(`./public${blog.image}`, ()=>{});\n                } catch (error) {\n                    console.error(\"Error deleting old image:\", error);\n                }\n            }\n            // Update image URL\n            blog.image = imgUrl;\n        }\n        // Save updated blog\n        await blog.save();\n        return NextResponse.json({\n            success: true,\n            msg: \"Blog Updated Successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating blog:\", error);\n        return NextResponse.json({\n            success: false,\n            msg: \"Error updating blog\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/blog/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/BlogModel.js":
/*!*********************************!*\
  !*** ./lib/models/BlogModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    category: {\n        type: String,\n        required: true\n    },\n    author: {\n        type: String,\n        required: true\n    },\n    image: {\n        type: String,\n        required: true\n    },\n    authorImg: {\n        type: String,\n        required: true\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst BlogModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).blog || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"blog\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Jsb2dNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFNO1FBQ0ZDLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBQyxhQUFZO1FBQ1JILE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRSxVQUFTO1FBQ0xKLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRyxRQUFPO1FBQ0hMLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSSxPQUFNO1FBQ0ZOLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSyxXQUFVO1FBQ05QLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBTSxNQUFLO1FBQ0RSLE1BQUtTO1FBQ0xDLFNBQVFELEtBQUtFLEdBQUc7SUFDcEI7QUFDSjtBQUVBLE1BQU1DLFlBQVlmLHdEQUFlLENBQUNpQixJQUFJLElBQUlqQixxREFBYyxDQUFDLFFBQU9DO0FBRWhFLGlFQUFlYyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQmxvZ01vZGVsLmpzPzhmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB0aXRsZTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBkZXNjcmlwdGlvbjp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBjYXRlZ29yeTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBhdXRob3I6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgaW1hZ2U6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgYXV0aG9ySW1nOntcbiAgICAgICAgdHlwZTpTdHJpbmcsXG4gICAgICAgIHJlcXVpcmVkOnRydWVcbiAgICB9LFxuICAgIGRhdGU6e1xuICAgICAgICB0eXBlOkRhdGUsXG4gICAgICAgIGRlZmF1bHQ6RGF0ZS5ub3coKVxuICAgIH1cbn0pXG5cbmNvbnN0IEJsb2dNb2RlbCA9IG1vbmdvb3NlLm1vZGVscy5ibG9nIHx8IG1vbmdvb3NlLm1vZGVsKCdibG9nJyxTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBCbG9nTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiYXV0aG9yIiwiaW1hZ2UiLCJhdXRob3JJbWciLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJCbG9nTW9kZWwiLCJtb2RlbHMiLCJibG9nIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/BlogModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/ImageModel.js":
/*!**********************************!*\
  !*** ./lib/models/ImageModel.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    filename: {\n        type: String,\n        required: true\n    },\n    path: {\n        type: String,\n        required: true\n    },\n    url: {\n        type: String,\n        required: true\n    },\n    contentType: {\n        type: String,\n        required: true\n    },\n    size: {\n        type: Number,\n        required: true\n    },\n    data: {\n        type: Buffer,\n        required: true\n    },\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.Mixed,\n        ref: \"blog\",\n        default: null\n    },\n    uploadDate: {\n        type: Date,\n        default: Date.now\n    }\n});\nconst ImageModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).image || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"image\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/ImageModel.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();