// Verify JWT token without using jsonwebtoken directly
export const verifyToken = async (token) => {
  try {
    // Simple token parsing - this is a temporary solution
    // In production, you should use proper JWT verification
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const decoded = JSON.parse(jsonPayload);
    return { userId: decoded.userId, role: decoded.role };
  } catch (error) {
    console.error("Token verification error:", error);
    return null;
  }
};

