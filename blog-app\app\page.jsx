'use client'
import BlogList from "@/Components/BlogList";
import Footer from "@/Components/Footer";
import Header from "@/Components/Header";
import { ToastContainer } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import React, { useState, useEffect } from 'react';
import { trackPageView } from '@/utils/analyticsUtils';

export default function Home() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    // Set mounted state to prevent hydration mismatch
    setIsMounted(true);
    // Track home page view
    trackPageView('/', 'home');
  }, []);
  
  // Show a simple loading state until client-side hydration is complete
  if (!isMounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }
  
  return (
    <>
      <ToastContainer theme="dark"/>
      <Header setSearchTerm={setSearchTerm}/>
      <BlogList searchTerm={searchTerm}/>
      <Footer/>
    </>
  )
}





