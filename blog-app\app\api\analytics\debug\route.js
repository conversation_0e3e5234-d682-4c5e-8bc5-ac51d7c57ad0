import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import AnalyticsModel from '@/lib/models/AnalyticsModel';

export async function GET() {
  try {
    await ConnectDB();
    
    // Count total records
    const totalRecords = await AnalyticsModel.countDocuments();
    
    // Get the most recent 5 records
    const recentRecords = await AnalyticsModel.find()
      .sort({ timestamp: -1 })
      .limit(5);
    
    // Get count by content type
    const countByType = await AnalyticsModel.aggregate([
      { $group: { _id: '$contentType', count: { $sum: 1 } } }
    ]);
    
    return NextResponse.json({
      success: true,
      totalRecords,
      recentRecords,
      countByType
    });
  } catch (error) {
    console.error('Error in analytics debug:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Error checking analytics data' 
    }, { status: 500 });
  }
}
