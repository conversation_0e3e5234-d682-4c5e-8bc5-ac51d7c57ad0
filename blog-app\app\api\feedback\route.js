import { ConnectDB } from "@/lib/config/db";
import { NextResponse } from "next/server";
import mongoose from "mongoose";

// Connect to database
const LoadDB = async () => {
  await ConnectDB();
}
LoadDB();

// Create Feedback model if it doesn't exist
const FeedbackSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  isRead: {
    type: Boolean,
    default: false
  }
});

const FeedbackModel = mongoose.models.Feedback || mongoose.model('Feedback', FeedbackSchema);

export async function POST(request) {
  try {
    const body = await request.json();
    const { name, email, message } = body;
    
    if (!name || !email || !message) {
      return NextResponse.json({ 
        success: false, 
        message: "All fields are required" 
      }, { status: 400 });
    }
    
    await FeedbackModel.create({ name, email, message });
    
    return NextResponse.json({ 
      success: true, 
      message: "Feedback submitted successfully" 
    });
  } catch (error) {
    console.error("Error submitting feedback:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to submit feedback" 
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const feedbacks = await FeedbackModel.find({}).sort({ createdAt: -1 });
    return NextResponse.json({ success: true, feedbacks });
  } catch (error) {
    console.error("Error fetching feedbacks:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to fetch feedbacks" 
    }, { status: 500 });
  }
}

export async function DELETE(request) {
  try {
    const id = request.nextUrl.searchParams.get("id");
    await FeedbackModel.findByIdAndDelete(id);
    return NextResponse.json({ 
      success: true, 
      message: "Feedback deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting feedback:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to delete feedback" 
    }, { status: 500 });
  }
}

export async function PUT(request) {
  try {
    const body = await request.json();
    const { id, isRead } = body;
    
    await FeedbackModel.findByIdAndUpdate(id, { isRead });
    
    return NextResponse.json({ 
      success: true, 
      message: "Feedback updated successfully" 
    });
  } catch (error) {
    console.error("Error updating feedback:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to update feedback" 
    }, { status: 500 });
  }
}
