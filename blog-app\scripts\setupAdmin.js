// This script can be run manually to ensure the admin user exists
// Run with: node scripts/setupAdmin.js

const axios = require('axios');

async function setupAdmin() {
  try {
    console.log('Setting up admin user...');
    const response = await axios.get('http://localhost:3000/api/auth');
    console.log(response.data.message);
  } catch (error) {
    console.error('Setup failed:', error.response?.data || error.message);
  }
}

setupAdmin();
