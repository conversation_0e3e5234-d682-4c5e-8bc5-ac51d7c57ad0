import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import AnalyticsModel from '@/lib/models/AnalyticsModel';
import { getUserFromToken } from '@/utils/authUtils';

export async function GET(request) {
  try {
    await ConnectDB();
    
    // Verify authentication
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('id');
    
    if (!blogId) {
      return NextResponse.json({ success: false, message: "Blog ID is required" }, { status: 400 });
    }
    
    // Get total views for this blog
    const totalViews = await AnalyticsModel.countDocuments({ blogId });
    
    // Get unique visitors for this blog
    const uniqueVisitors = await AnalyticsModel.distinct('ipHash', { blogId }).then(ips => ips.length);
    
    // Get views over time (daily)
    const viewsOverTime = await AnalyticsModel.aggregate([
      { $match: { blogId: blogId } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    return NextResponse.json({
      success: true,
      analytics: {
        totalViews,
        uniqueVisitors,
        viewsOverTime
      }
    });
  } catch (error) {
    console.error("Error fetching blog analytics:", error);
    return NextResponse.json({ success: false, message: "Failed to fetch blog analytics" }, { status: 500 });
  }
}

