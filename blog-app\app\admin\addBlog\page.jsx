'use client'
import { assets } from '@/Assets/assets'
import axios from 'axios'
import Image from 'next/image'
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import Link from 'next/link'
import BlogTableItem from '@/Components/AdminComponents/BlogTableItem'

const BlogManagementPage = () => {
    const [activeTab, setActiveTab] = useState('add'); // 'add' or 'manage'
    const [image, setImage] = useState(false);
    const [categories, setCategories] = useState([]);
    const [authors, setAuthors] = useState([]);
    const [loading, setLoading] = useState(true);
    const [blogs, setBlogs] = useState([]);
    const [data, setData] = useState({
        title: "",
        description: "",
        category: "",
        author: "",
        authorId: "",
        authorImg: "",
        commentsEnabled: true
    });
    const [showBlogSelector, setShowBlogSelector] = useState(false);
    const [allBlogs, setAllBlogs] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');

    // Image insertion states
    const [showImageSelector, setShowImageSelector] = useState(false);
    const [allImages, setAllImages] = useState([]);
    const [imageSearchTerm, setImageSearchTerm] = useState('');
    const [imageUploadLoading, setImageUploadLoading] = useState(false);
    const [selectedImageFile, setSelectedImageFile] = useState(null);
    const [tempBlogId, setTempBlogId] = useState(null);

    useEffect(() => {
        fetchCategories();
        fetchAuthors();
        
        if (activeTab === 'manage') {
            fetchBlogs();
        }
    }, [activeTab]);

    // Generate a temporary blog ID for image uploads when component mounts
    useEffect(() => {
        setTempBlogId('temp_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11));
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await axios.get('/api/categories');
            if (response.data.success && response.data.categories.length > 0) {
                setCategories(response.data.categories);
                setData(prev => ({
                    ...prev,
                    category: response.data.categories[0].name
                }));
            } else {
                toast.error("No categories found. Please add categories in Settings.");
                setCategories([]);
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
            toast.error("Failed to load categories");
            setCategories([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchAuthors = async () => {
        try {
            const response = await axios.get('/api/authors');
            if (response.data.success && response.data.authors.length > 0) {
                setAuthors(response.data.authors);
                setData(prev => ({
                    ...prev,
                    author: response.data.authors[0].name,
                    authorId: response.data.authors[0]._id,
                    authorImg: response.data.authors[0].image || "/author_img.png"
                }));
            } else {
                toast.error("No authors found. Please add authors in Settings.");
                setAuthors([]);
            }
        } catch (error) {
            console.error("Error fetching authors:", error);
            toast.error("Failed to load authors");
            setAuthors([]);
        }
    };

    const fetchBlogs = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/blog');
            setBlogs(response.data.blogs || []);
        } catch (error) {
            console.error("Error fetching blogs:", error);
            toast.error("Failed to load blogs");
        } finally {
            setLoading(false);
        }
    };

    const onChangeHandler = (e) => {
        const { name, value } = e.target;
        
        if (name === 'authorId') {
            const selectedAuthor = authors.find(author => author._id === value);
            if (selectedAuthor) {
                setData({
                    ...data,
                    author: selectedAuthor.name,
                    authorId: selectedAuthor._id,
                    authorImg: selectedAuthor.image || "/author_img.png"
                });
            }
        } else {
            setData({
                ...data,
                [name]: value
            });
        }
    };

    const onSubmitHandler = async (e) => {
        e.preventDefault();
        
        if (!image) {
            toast.error("Please select a blog thumbnail image");
            return;
        }
        
        if (!data.title.trim()) {
            toast.error("Please enter a blog title");
            return;
        }
        
        const formData = new FormData();
        formData.append('title', data.title);
        formData.append('description', data.description);
        formData.append('category', data.category);
        formData.append('author', data.author);
        formData.append('authorId', data.authorId);
        formData.append('authorImg', data.authorImg);
        formData.append('commentsEnabled', data.commentsEnabled);
        formData.append('image', image);
        formData.append('tempBlogId', tempBlogId);
        
        try {
            setLoading(true);
            const response = await axios.post('/api/blog', formData);
            if (response.data.success) {
                toast.success(response.data.msg || "Blog added successfully");
                setImage(false);
                setData({
                    title: "",
                    description: "",
                    category: categories.length > 0 ? categories[0].name : "",
                    author: authors.length > 0 ? authors[0].name : "",
                    authorId: authors.length > 0 ? authors[0]._id : "",
                    authorImg: authors.length > 0 ? (authors[0].image || "/author_img.png") : "/author_img.png",
                    commentsEnabled: true
                });
                
                // Reset temp blog ID and clear images
                setTempBlogId('temp_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11));
                setAllImages([]);

                // Switch to manage tab and refresh blog list
                setActiveTab('manage');
            } else {
                toast.error(response.data.msg || "Error adding blog");
            }
        } catch (error) {
            console.error("Error submitting blog:", error);
            toast.error("Failed to add blog");
        } finally {
            setLoading(false);
        }
    };

    const deleteBlog = async (mongoId) => {
        if (!window.confirm("Are you sure you want to delete this blog?")) {
            return;
        }
        
        try {
            setLoading(true);
            const response = await axios.delete('/api/blog', {
                params: {
                    id: mongoId
                }
            });
            toast.success(response.data.msg || "Blog deleted successfully");
            fetchBlogs();
        } catch (error) {
            console.error("Error deleting blog:", error);
            toast.error("Failed to delete blog");
        } finally {
            setLoading(false);
        }
    };

    const fetchAllBlogs = async () => {
        try {
            const response = await axios.get('/api/blog');
            setAllBlogs(response.data.blogs || []);
        } catch (error) {
            console.error("Error fetching blogs:", error);
        }
    };

    const insertBlogMention = (blogId, blogTitle) => {
        const mention = `[[${blogId}|${blogTitle}]]`;
        const textarea = document.getElementById('blog-description');
        const cursorPos = textarea.selectionStart;
        const textBefore = data.description.substring(0, cursorPos);
        const textAfter = data.description.substring(cursorPos);

        setData({
            ...data,
            description: textBefore + mention + textAfter
        });

        setShowBlogSelector(false);

        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);
        }, 100);
    };

    // Image-related functions
    const fetchAllImages = async () => {
        if (!tempBlogId) return;

        try {
            const response = await axios.get('/api/images', {
                params: { blogId: tempBlogId, limit: 50 }
            });
            if (response.data.success) {
                setAllImages(response.data.images);
            }
        } catch (error) {
            console.error("Error fetching images:", error);
            toast.error("Failed to fetch images");
        }
    };

    const handleImageUpload = async () => {
        if (!selectedImageFile) {
            toast.error("Please select an image file");
            return;
        }

        setImageUploadLoading(true);
        try {
            const formData = new FormData();
            formData.append('image', selectedImageFile);
            formData.append('blogId', tempBlogId || 'new');

            const response = await axios.post('/api/upload/image', formData);

            if (response.data.success) {
                toast.success("Image uploaded successfully");
                setSelectedImageFile(null);
                // Refresh the images list
                await fetchAllImages();
            } else {
                toast.error(response.data.message || "Failed to upload image");
            }
        } catch (error) {
            console.error("Error uploading image:", error);
            toast.error("Failed to upload image");
        } finally {
            setImageUploadLoading(false);
        }
    };

    const deleteImage = async (imageId, imageUrl) => {
        if (!window.confirm("Are you sure you want to delete this image? This action cannot be undone.")) {
            return;
        }

        try {
            const response = await axios.delete(`/api/images/${imageId}`);
            if (response.data.success) {
                toast.success("Image deleted successfully");
                // Refresh the images list
                await fetchAllImages();
            } else {
                toast.error(response.data.message || "Failed to delete image");
            }
        } catch (error) {
            console.error("Error deleting image:", error);
            toast.error("Failed to delete image");
        }
    };

    const insertImageReference = (imageUrl, filename) => {
        const imageRef = `{{image:${imageUrl}|${filename}}}`;
        const textarea = document.getElementById('blog-description');
        const cursorPos = textarea.selectionStart;
        const textBefore = data.description.substring(0, cursorPos);
        const textAfter = data.description.substring(cursorPos);

        setData({
            ...data,
            description: textBefore + imageRef + textAfter
        });

        setShowImageSelector(false);

        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);
        }, 100);
    };

    return (
        <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
            <h1 className='text-2xl font-bold mb-6'>Blog Management</h1>
            
            {/* Tabs */}
            <div className="flex border-b border-gray-300 mb-6">
                <button 
                    className={`py-3 px-6 font-medium rounded-t-lg ${
                        activeTab === 'add' 
                            ? 'bg-black text-white' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setActiveTab('add')}
                >
                    Add New Blog
                </button>
                <button 
                    className={`py-3 px-6 font-medium rounded-t-lg ml-2 ${
                        activeTab === 'manage' 
                            ? 'bg-black text-white' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setActiveTab('manage')}
                >
                    Manage Blogs
                </button>
            </div>
            
            {/* Add Blog Form */}
            {activeTab === 'add' && (
                <form onSubmit={onSubmitHandler} className='max-w-[800px]'>
                    <div className='bg-white p-6 rounded-lg shadow-md'>
                        <h2 className="text-xl font-semibold mb-4">Create New Blog Post</h2>
                        
                        <div className='mb-4'>
                            <p className='text-sm font-medium mb-2'>Upload Thumbnail <span className="text-red-500">*</span></p>
                            <label htmlFor="image" className="cursor-pointer block">
                                <Image 
                                    className='border border-gray-300 rounded-md' 
                                    src={!image ? assets.upload_area : URL.createObjectURL(image)} 
                                    width={200} 
                                    height={120} 
                                    alt=''
                                    style={{ objectFit: 'cover', height: '120px' }}
                                />
                            </label>
                            <input onChange={(e) => setImage(e.target.files[0])} type="file" id='image' hidden required />
                        </div>
                        
                        <div className='mb-4'>
                            <label className='block text-sm font-medium mb-2'>Blog Title <span className="text-red-500">*</span></label>
                            <input 
                                name='title' 
                                onChange={onChangeHandler} 
                                value={data.title} 
                                className='w-full px-4 py-3 border rounded-md' 
                                type="text" 
                                placeholder='Type here' 
                                required 
                            />
                        </div>
                        
                        <div className='mb-4 relative'>
                            <label className='block text-sm font-medium mb-2'>Blog Description</label>
                            <div className="flex items-start">
                                <textarea 
                                    id="blog-description"
                                    name='description' 
                                    onChange={onChangeHandler} 
                                    value={data.description} 
                                    className='w-full px-4 py-3 border rounded-md' 
                                    placeholder='Write content here' 
                                    rows={6} 
                                    required 
                                />
                            </div>
                            <div className="mt-2 flex items-center flex-wrap gap-4">
                                <button
                                    type="button"
                                    onClick={() => {
                                        fetchAllBlogs();
                                        setShowBlogSelector(true);
                                    }}
                                    className="text-sm flex items-center text-blue-600 hover:text-blue-800"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                                    </svg>
                                    Mention another blog
                                </button>

                                <button
                                    type="button"
                                    onClick={() => {
                                        fetchAllImages();
                                        setShowImageSelector(true);
                                    }}
                                    className="text-sm flex items-center text-green-600 hover:text-green-800"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    Insert image
                                </button>

                                <div className="text-xs text-gray-500">
                                    <span>Formats: [[blogId|blogTitle]] | {`{{image:url|filename}}`}</span>
                                </div>
                            </div>
                            
                            {/* Blog selector modal */}
                            {showBlogSelector && (
                                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                    <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto">
                                        <div className="flex justify-between items-center mb-4">
                                            <h3 className="text-lg font-semibold">Select a blog to mention</h3>
                                            <button 
                                                onClick={() => setShowBlogSelector(false)}
                                                className="text-gray-500 hover:text-gray-700"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>
                                        
                                        <input
                                            type="text"
                                            placeholder="Search blogs..."
                                            className="w-full px-4 py-2 border rounded-md mb-4"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                        
                                        <div className="divide-y">
                                            {allBlogs
                                                .filter(blog => blog.title.toLowerCase().includes(searchTerm.toLowerCase()))
                                                .map(blog => (
                                                    <div 
                                                        key={blog._id} 
                                                        className="py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center"
                                                        onClick={() => insertBlogMention(blog._id, blog.title)}
                                                    >
                                                        <div className="w-12 h-12 relative mr-3">
                                                            <Image 
                                                                src={blog.image} 
                                                                alt={blog.title}
                                                                fill
                                                                className="object-cover rounded"
                                                            />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-medium">{blog.title}</h4>
                                                            <p className="text-sm text-gray-500">{blog.category}</p>
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Image selector modal */}
                            {showImageSelector && (
                                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                    <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto">
                                        <div className="flex justify-between items-center mb-4">
                                            <h3 className="text-lg font-semibold">Insert Image</h3>
                                            <button
                                                onClick={() => setShowImageSelector(false)}
                                                className="text-gray-500 hover:text-gray-700"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>

                                        {/* Upload new image section */}
                                        <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                                            <h4 className="font-medium mb-3">Upload New Image</h4>
                                            <div className="flex items-center gap-4">
                                                <input
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={(e) => setSelectedImageFile(e.target.files[0])}
                                                    className="flex-1"
                                                />
                                                <button
                                                    type="button"
                                                    onClick={handleImageUpload}
                                                    disabled={!selectedImageFile || imageUploadLoading}
                                                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                                >
                                                    {imageUploadLoading ? 'Uploading...' : 'Upload'}
                                                </button>
                                            </div>
                                        </div>

                                        {/* Search existing images */}
                                        <input
                                            type="text"
                                            placeholder="Search images..."
                                            className="w-full px-4 py-2 border rounded-md mb-4"
                                            value={imageSearchTerm}
                                            onChange={(e) => setImageSearchTerm(e.target.value)}
                                        />

                                        {/* Images grid */}
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                            {allImages
                                                .filter(image => image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase()))
                                                .map(image => (
                                                    <div
                                                        key={image._id}
                                                        className="border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group"
                                                    >
                                                        <div
                                                            className="aspect-square relative mb-2"
                                                            onClick={() => insertImageReference(image.url, image.filename)}
                                                        >
                                                            <Image
                                                                src={image.url}
                                                                alt={image.filename}
                                                                fill
                                                                className="object-cover rounded"
                                                            />
                                                            {/* Delete button */}
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    deleteImage(image._id, image.url);
                                                                }}
                                                                className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                                                title="Delete image"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                        <div onClick={() => insertImageReference(image.url, image.filename)}>
                                                            <p className="text-xs text-gray-600 truncate">{image.filename}</p>
                                                            <p className="text-xs text-gray-400">{new Date(image.uploadDate).toLocaleDateString()}</p>
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>

                                        {allImages.length === 0 && (
                                            <div className="text-center py-8 text-gray-500">
                                                No images found. Upload your first image above.
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className='mb-4'>
                            <label className='block text-sm font-medium mb-2'>Blog Category</label>
                            {loading ? (
                                <p>Loading categories...</p>
                            ) : categories.length > 0 ? (
                                <select 
                                    name="category" 
                                    onChange={onChangeHandler} 
                                    value={data.category} 
                                    className='w-full px-4 py-3 border rounded-md text-gray-700'
                                    required
                                >
                                    {categories.map(category => (
                                        <option key={category._id} value={category.name}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                            ) : (
                                <p className="text-red-500">No categories available. Please add categories in Settings.</p>
                            )}
                        </div>
                        
                        <div className='mb-6'>
                            <label className='block text-sm font-medium mb-2'>Blog Author</label>
                            {loading ? (
                                <p>Loading authors...</p>
                            ) : authors.length > 0 ? (
                                <div className="flex items-center gap-4">
                                    <select 
                                        name="authorId" 
                                        onChange={onChangeHandler} 
                                        value={data.authorId} 
                                        className='w-full px-4 py-3 border rounded-md text-gray-700'
                                        required
                                    >
                                        {authors.map(author => (
                                            <option key={author._id} value={author._id}>
                                                {author.name}
                                            </option>
                                        ))}
                                    </select>
                                    
                                    {data.authorId && (
                                        <div className="flex items-center gap-2">
                                            <img 
                                                src={data.authorImg} 
                                                alt={data.author} 
                                                className="w-10 h-10 rounded-full object-cover border border-gray-200" 
                                            />
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-red-500">No authors available. Please add authors in Settings.</p>
                            )}
                        </div>

                        <div className='mb-6'>
                            <label className='flex items-center gap-3 cursor-pointer'>
                                <input
                                    type="checkbox"
                                    name="commentsEnabled"
                                    checked={data.commentsEnabled}
                                    onChange={(e) => setData(prev => ({...prev, commentsEnabled: e.target.checked}))}
                                    className='w-4 h-4 text-black border-gray-300 rounded focus:ring-black'
                                />
                                <span className='text-sm font-medium text-gray-700'>
                                    Enable comments for this blog post
                                </span>
                            </label>
                            <p className='text-xs text-gray-500 mt-1 ml-7'>
                                When enabled, readers can comment on this blog post
                            </p>
                        </div>

                        <button
                            type="submit"
                            className='w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors'
                            disabled={loading || categories.length === 0 || authors.length === 0}
                        >
                            {loading ? 'Creating...' : 'Create Blog Post'}
                        </button>
                    </div>
                </form>
            )}
            
            {/* Manage Blogs Table */}
            {activeTab === 'manage' && (
                <div className='max-w-[850px] bg-white p-6 rounded-lg shadow-md'>
                    <h2 className="text-xl font-semibold mb-4">Manage Blog Posts</h2>
                    
                    {loading ? (
                        <div className="flex justify-center items-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
                            <span className="ml-2">Loading blogs...</span>
                        </div>
                    ) : blogs.length > 0 ? (
                        <div className="relative overflow-x-auto border border-gray-300 rounded-lg">
                            <table className="w-full text-sm text-gray-500 table-fixed">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Title
                                        </th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Author
                                        </th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Comments
                                        </th>
                                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {blogs.map((item, index) => (
                                        <BlogTableItem
                                            key={index}
                                            mongoId={item._id}
                                            title={item.title}
                                            author={item.author}
                                            authorImg={item.authorImg}
                                            date={item.date}
                                            commentsEnabled={item.commentsEnabled !== undefined ? item.commentsEnabled : true}
                                            deleteBlog={deleteBlog}
                                        />
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            <p>No blogs found.</p>
                            <button 
                                onClick={() => setActiveTab('add')}
                                className="mt-4 text-blue-600 hover:underline"
                            >
                                Add your first blog
                            </button>
                        </div>
                    )}
                    
                    {/* Blog Count Summary */}
                    {blogs.length > 0 && (
                        <div className="mt-4 text-sm text-gray-500">
                            <p>Showing {blogs.length} blog posts</p>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default BlogManagementPage;
