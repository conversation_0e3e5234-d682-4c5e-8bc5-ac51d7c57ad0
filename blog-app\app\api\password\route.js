import { ConnectDB } from "@/lib/config/db";
import UserModel from "@/lib/models/UserModel";
import { NextResponse } from "next/server";

// Initialize database connection
const LoadDB = async () => {
    await ConnectDB();
}
LoadDB();

export async function PUT(request) {
    try {
        const { userId, currentPassword, newPassword } = await request.json();
        
        if (!userId || !currentPassword || !newPassword) {
            return NextResponse.json({
                success: false,
                message: "Missing required fields"
            }, { status: 400 });
        }
        
        // Find the user
        const user = await UserModel.findById(userId);
        if (!user) {
            return NextResponse.json({
                success: false,
                message: "User not found"
            }, { status: 404 });
        }
        
        // Verify current password
        if (user.password !== currentPassword) {
            return NextResponse.json({
                success: false,
                message: "Current password is incorrect"
            }, { status: 401 });
        }
        
        // Update password
        user.password = newPassword;
        await user.save();
        
        return NextResponse.json({
            success: true,
            message: "Password updated successfully"
        });
    } catch (error) {
        console.error("Password update error:", error);
        return NextResponse.json({
            success: false,
            message: "Server error"
        }, { status: 500 });
    }
}