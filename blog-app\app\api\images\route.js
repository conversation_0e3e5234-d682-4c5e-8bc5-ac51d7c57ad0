import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import ImageModel from "@/lib/models/ImageModel";

export async function GET(request) {
  try {
    await ConnectDB();

    const blogId = request.nextUrl.searchParams.get("blogId");
    const page = parseInt(request.nextUrl.searchParams.get("page")) || 1;
    const limit = parseInt(request.nextUrl.searchParams.get("limit")) || 20;
    const search = request.nextUrl.searchParams.get("search") || "";

    let query = {};
    if (blogId && blogId !== 'all') {
      query.blogId = blogId;
    }

    // Add search functionality
    if (search) {
      query.filename = { $regex: search, $options: 'i' };
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get images but exclude the binary data to reduce response size
    const images = await ImageModel.find(query)
      .select('-data')
      .sort({ uploadDate: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalImages = await ImageModel.countDocuments(query);
    const totalPages = Math.ceil(totalImages / limit);

    return NextResponse.json({
      success: true,
      images: images,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalImages: totalImages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error("Error retrieving images:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to retrieve images"
    }, { status: 500 });
  }
}