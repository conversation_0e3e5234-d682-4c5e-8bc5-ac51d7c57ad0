'use client'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

const BlogListRedirect = () => {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the new combined blog management page with the manage tab active
    router.push('/admin/addBlog?tab=manage')
  }, [router])

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className="text-2xl font-bold mb-4">Redirecting...</h1>
      <p>Please wait while we redirect you to the new Blog Management page.</p>
    </div>
  )
}

export default BlogListRedirect
