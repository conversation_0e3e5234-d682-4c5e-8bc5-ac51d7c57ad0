import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import BlogModel from '@/lib/models/BlogModel';
import UserModel from '@/lib/models/UserModel';
import EmailModel from '@/lib/models/EmailModel';

export async function GET() {
  try {
    await ConnectDB();
    
    // Fetch recent blogs (last 5)
    const recentBlogs = await BlogModel.find()
      .sort({ date: -1 })
      .limit(5)
      .select('title date');
    
    // Fetch recent users (last 5)
    const recentUsers = await UserModel.find()
      .sort({ date: -1 })
      .limit(5)
      .select('name email date');
    
    // Fetch recent subscriptions (last 5)
    const recentSubscriptions = await EmailModel.find()
      .sort({ date: -1 })
      .limit(5)
      .select('email date');
    
    // Combine and format all activities
    let allActivities = [
      ...recentBlogs.map(blog => ({
        type: 'New Blog',
        message: `New blog post: "${blog.title}"`,
        timestamp: blog.date
      })),
      ...recentUsers.map(user => ({
        type: 'New User',
        message: `New user registered: ${user.name || user.email}`,
        timestamp: user.date
      })),
      ...recentSubscriptions.map(sub => ({
        type: 'Subscription',
        message: `New email subscription: ${sub.email}`,
        timestamp: sub.date
      }))
    ];
    
    // Sort by date (newest first)
    allActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Take only the 10 most recent activities
    allActivities = allActivities.slice(0, 10);
    
    return NextResponse.json({
      success: true,
      activities: allActivities
    });
  } catch (error) {
    console.error('Error fetching activity:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch recent activity'
    }, { status: 500 });
  }
}
