import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowIcon } from './Icons';

const TrendingBlogs = ({ blogs, currentBlogId }) => {
  // Filter out the current blog and limit to 12 blogs
  const filteredBlogs = blogs
    .filter(blog => blog._id !== currentBlogId)
    .slice(0, 12);
  
  // State for carousel
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const carouselRef = useRef(null);
  const timerRef = useRef(null);
  
  // Number of visible items and peek amount based on screen size
  const [visibleItems, setVisibleItems] = useState(4);
  const [peekAmount, setPeekAmount] = useState(80); // pixels to show of the next item
  
  // Update visible items on window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setVisibleItems(1);
        setPeekAmount(40);
      } else if (window.innerWidth < 768) {
        setVisibleItems(2);
        setPeekAmount(60);
      } else if (window.innerWidth < 1024) {
        setVisibleItems(3);
        setPeekAmount(70);
      } else {
        setVisibleItems(4);
        setPeekAmount(80);
      }
    };
    
    // Initial call
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Auto-slide functionality
  useEffect(() => {
    if (filteredBlogs.length <= visibleItems) return;
    
    const startTimer = () => {
      timerRef.current = setTimeout(() => {
        if (!isAnimating) {
          nextSlide();
        }
      }, 2000); // Changed from 1000 to 2000 ms (2 seconds)
    };
    
    startTimer();
    
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [currentIndex, filteredBlogs.length, visibleItems, isAnimating]);
  
  // Calculate item width based on visible items and peek amount
  const calculateItemWidth = () => {
    if (!carouselRef.current) return `${100 / filteredBlogs.length}%`;
    
    const containerWidth = carouselRef.current.offsetWidth;
    const itemWidth = (containerWidth - peekAmount) / visibleItems;
    return `${itemWidth}px`;
  };
  
  // Handle manual navigation
  const nextSlide = () => {
    if (isAnimating || filteredBlogs.length <= visibleItems) return;
    
    setIsAnimating(true);
    setCurrentIndex((prevIndex) => 
      prevIndex === filteredBlogs.length - visibleItems ? 0 : prevIndex + 1
    );
    
    // Reduce animation time to match faster slide interval
    setTimeout(() => {
      setIsAnimating(false);
    }, 300); // Changed from 500 to 300 ms
  };
  
  const prevSlide = () => {
    if (isAnimating || filteredBlogs.length <= visibleItems) return;
    
    setIsAnimating(true);
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? filteredBlogs.length - visibleItems : prevIndex - 1
    );
    
    // Reduce animation time to match faster slide interval
    setTimeout(() => {
      setIsAnimating(false);
    }, 300); // Changed from 500 to 300 ms
  };
  
  // Reset timer when manually navigating
  const handleNavigation = (callback) => {
    if (timerRef.current) clearTimeout(timerRef.current);
    callback();
    timerRef.current = setTimeout(() => {
      nextSlide();
    }, 2000); // Changed from 1000 to 2000 ms (2 seconds)
  };
  
  if (filteredBlogs.length === 0) {
    return null;
  }

  // Calculate the slide offset with peek amount
  const slideOffset = () => {
    if (!carouselRef.current) return `${currentIndex * (100 / visibleItems)}%`;
    
    const containerWidth = carouselRef.current.offsetWidth;
    const itemWidth = (containerWidth - peekAmount) / visibleItems;
    return `${currentIndex * itemWidth}px`;
  };

  return (
    <div className="my-12 border-t border-gray-200 pt-10">
      <h2 className="text-xl sm:text-2xl font-bold mb-6 text-center">Trending Articles</h2>
      
      <div className="relative px-4">
        {/* Navigation arrows */}
        {filteredBlogs.length > visibleItems && (
          <>
            <button 
              onClick={() => handleNavigation(prevSlide)}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md"
              aria-label="Previous"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button 
              onClick={() => handleNavigation(nextSlide)}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md"
              aria-label="Next"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}
        
        {/* Carousel container with peek effect */}
        <div 
          className="overflow-hidden" 
          ref={carouselRef}
          style={{ 
            paddingRight: `${peekAmount}px`,
          }}
        >
          <div 
            className="flex transition-transform duration-300 ease-in-out" // Changed from duration-500 to duration-300
            style={{ 
              transform: `translateX(-${slideOffset()})`,
            }}
          >
            {filteredBlogs.map((blog) => (
              <div 
                key={blog._id} 
                className="pr-4 flex-shrink-0"
                style={{ 
                  width: calculateItemWidth(),
                }}
              >
                <div className="bg-white border border-gray-200 transition-all hover:shadow-md h-full">
                  <Link href={`/blogs/${blog._id}`}>
                    <div className="relative h-36 border-b border-gray-200">
                      <Image 
                        src={blog.image} 
                        alt={blog.title} 
                        fill 
                        className="object-cover" 
                      />
                    </div>
                  </Link>
                  <div className="p-3">
                    <p className="px-1.5 py-0.5 mb-1.5 inline-block bg-gray-100 text-gray-800 text-xs">{blog.category}</p>
                    <h3 className="text-sm font-medium mb-1.5 line-clamp-2">{blog.title}</h3>
                    <Link href={`/blogs/${blog._id}`} className="inline-flex items-center text-xs font-semibold text-gray-700">
                      Read more <ArrowIcon className="ml-1 w-3 h-3" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Pagination dots */}
        {filteredBlogs.length > visibleItems && (
          <div className="flex justify-center mt-4">
            {Array.from({ length: filteredBlogs.length - visibleItems + 1 }).map((_, index) => (
              <button
                key={index}
                onClick={() => handleNavigation(() => {
                  setIsAnimating(true);
                  setCurrentIndex(index);
                  setTimeout(() => setIsAnimating(false), 500);
                })}
                className={`h-2 w-2 mx-1 rounded-full ${currentIndex === index ? 'bg-gray-800' : 'bg-gray-300'}`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TrendingBlogs;








